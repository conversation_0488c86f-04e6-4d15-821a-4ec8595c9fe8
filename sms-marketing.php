<?php $MENU='sms'; require_once('header.php'); ?>

<style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');
      
      
        /* Banner styles */
        .banner {
            /* background-image: url(''); */
          display: flex;
          /* align-items: center; */
          color: black;
          margin-top: 120px;
          background-color: #ffde59;
        }
        .banner-text {
          /* max-width: 600px; */
          /* padding: 25px 30px; */
          border-radius: 12px;
            position: absolute;
            margin: 0px 60px;
            max-width: 50%;
    padding: 25px 58px;
    margin-top: 146px;
        }
        .banner-text h1 {
          margin: 0 0 15px;
          font-size: 80px;
          font-weight: 700;
          letter-spacing: 1.2px;
          color: black;

        }
        .banner-text p {
            font-size: 14px;
          font-weight: 400;
          line-height: 1.4;
        }
      
        /* Container */
        .containerx {
          max-width: 85%;
          margin: 0 auto;
          padding: 60px 20px 100px;
        }
      
        /* Each section */
        section {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 90px;
          gap: 40px;
        }
      
        /* Alternate section direction */
        section.even {
          flex-direction: row-reverse;
        }
      
        .content {
          flex: 1 1 520px;
        }
        .content h2 {
          font-weight: 700;
          font-size: 24px;
          margin-bottom: 22px;
          color: #fed557;
        }
        .content p {
          font-size: 18px;
          margin-bottom: 20px;
          color: #444;
          font-family: system-ui;
        }
        .content ul {
          list-style-type: disc;
          padding-left: 22px;
          color: #444;
          font-size: 1.15rem;
          font-family: system-ui;
        }
        .content ul li {
          margin-bottom: 14px;
          font-size: 18px;
          font-family: system-ui;
        }
      
        /* Image container */
        .image-container {
          flex: 1 1 520px;
          text-align: center;
        }
        .image-container img {
          max-width: 100%;
          border-radius: 20px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
          transition: transform 0.3s ease;
          max-height: 420px;
          object-fit: cover;
        }
        .image-container img:hover {
          transform: scale(1.07);
        }
      
        /* Animation base and effect */
        .slide-in-left, .slide-in-right {
          opacity: 0;
          transform: translateX(0);
          transition: transform 0.9s ease, opacity 0.9s ease;
        }
        .slide-in-left {
          transform: translateX(-60px);
        }
        .slide-in-right {
          transform: translateX(60px);
        }
        .slide-in-left.active,
        .slide-in-right.active {
          opacity: 1;
          transform: translateX(0);
        }

        .banner img{
            width: 100%;
            height: 70%;
            margin: 0px auto !important;
        }

  
      
        /* Responsive for smaller devices */
        @media (max-width: 860px) {
          section {
            flex-direction: column !important;
            margin-bottom: 70px;
          }
          .content, .image-container {
            flex: 1 1 100%;
            padding: 10px 0;
          }
          .banner-text h1 {
            font-size: 55px;
            color: white;
          }
          .banner-text {
            padding: 0px ;
            color: white;

          }
          .banner-text p {
            font-size: 12px;
            color: white;

          }
          .banner{
            margin-top: 0px;
            color: white;

          }
          .banner img{
            width: 900px;
            height: 650px;
          }
        }
      
      
      </style>
         

    <!-- <div id="home" class="parallax first-section" data-stellar-background-ratio="0.4" style="background-image:url('uploads/parallax_12.jpg');"> -->
      <div id="home" class="parallax first-section" data-stellar-background-ratio="0.4" style="background-color:#fed557;">
        <div class="container">
            <div class="row">
                <div class="col-md-6 col-sm-12">
                    <div class="big-tagline">
                        <h2 style="color:black !important;">SMS Marketing</h2>
                        <p style="color:black !important;     margin-top: -25px;" class="lead">Engage your customers personally and instantly. Reach the right audience with effective, measurable SMS campaigns that boost your conversions.</p>
                        <!-- <a href="https://my.surecliq.com/account/signup" class="btn btn-light btn-radius btn-brd ban-btn" style="border-radius: 50px;">Start Sending</a> -->
                        <a href="https://name.surecliq.com/console/#signup" class="btn btn-light btn-radius btn-brd ban-btn" style="border-radius: 50px;">Start Sending</a>
                    </div>
                </div>


                <div class="col-md-6 col-sm-12">
                  <img style="margin-top: 50px;" src="uploads/SureCLIQ-short.png" alt="" class="img-responsive">
              </div>

                <!-- <div class="app_iphone_02 wow slideInUp hidden-xs hidden-sm" data-wow-duration="1s"
                    data-wow-delay="0.5s">
                    <img src="uploads/rocket.png" alt="" class="img-responsive">
                </div> -->
            </div><!-- end row -->
        </div><!-- end container -->
    </div><!-- end section -->

    <svg id="clouds" class="hidden-xs" xmlns="http://www.w3.org/2000/svg" version="1.1" width="100%" height="100"
        viewBox="0 0 85 100" preserveAspectRatio="none">
        <path d="M-5 100 Q 0 20 5 100 Z
            M0 100 Q 5 0 10 100
            M5 100 Q 10 30 15 100
            M10 100 Q 15 10 20 100
            M15 100 Q 20 30 25 100
            M20 100 Q 25 -10 30 100
            M25 100 Q 30 10 35 100
            M30 100 Q 35 30 40 100
            M35 100 Q 40 10 45 100
            M40 100 Q 45 50 50 100
            M45 100 Q 50 20 55 100
            M50 100 Q 55 40 60 100
            M55 100 Q 60 60 65 100
            M60 100 Q 65 50 70 100
            M65 100 Q 70 20 75 100
            M70 100 Q 75 45 80 100
            M75 100 Q 80 30 85 100
            M80 100 Q 85 20 90 100
            M85 100 Q 90 50 95 100
            M90 100 Q 95 25 100 100
            M95 100 Q 100 15 105 100 Z">
        </path>
    </svg>

    
      <div class="containerx" role="main">
      
       
        <!-- Section 2: Why Use SMS Marketing -->
        <section class="even">
          <div class="content slide-in-right">
            <h2 style="    color: #dab648;    position: relative;
    left: 17px;
">Why SMS Marketing?</h2>
            <ul>
              <li><strong>Instant Connection:</strong> SMS messages boast a 98% open rate, ensuring your message is seen almost immediately after delivery.</li>
              <li><strong>High Engagement:</strong> Text messages generate much higher response rates than emails or social media posts.</li>
              <li><strong>Cost-Effective:</strong> Compared to traditional advertising, SMS marketing is affordable with measurable ROI.</li>
              <li><strong>Personalized Communication:</strong> Segment your audience and tailor messages to drive stronger customer relationships.</li>
            </ul>
          </div>
          <div class="image-container slide-in-left">
            <img src="uploads/sms2.png" alt="Person working on marketing campaign laptop" />
          </div>
        </section>







      <style>


@media (max-width: 860px) {
  .fddd h2{
    position: relative;
    left:0px !important;
    /* background-color:red; */
  }

}
        .h5class{
          font-size: 20px;
    display: block;
    left: auto;
    right: auto;
    text-align: center;
        }

        .fddd{
          display: block;
          text-align: center;
          
        }
        .fddd h2{
          color:#dab648; 
          font-size:33px;  
            position: relative;
        left: 56px; 
        }
        .fddd p{
          font-family: system-ui;
    text-align: justify;
        }
      </style>
        <!-- Section 3: Features of SMS Marketing -->
        <section>
          <div class="content  fddd">
          <h2 style="
">Make your Marketing Campaigns more effective through SureCLIQ SMS Marketing Platform</h2>

            <div class="row">
                <div class="col-md-4 image-container" style="display:block; text-align:center;margin-top:65px;" >
<img src="uploads/player-a.png" style="width: 70%; 
    border-radius: 28px;     box-shadow: 4px 4px 8px 0px black;" />
                </div>
                <div class="col-md-8" style="margin-top:65px;">
                <h5 class="h5class" ><strong>Player Acquisition</strong> </h5>
          <p>The goal of player acquisition is to draw in new players to your platform using various promotional offers using SureCLIQ SMS Marketing platform. This will ensure more revenue, which is essential to the success of your iGaming/eSports business.
<br/>
<br/>
Use messages like "Welcome Bonuses", "Demo games". Potential players who see that there are freebies available when they sign up will be more likely to register and start playing also, this allows them to check it out without any commitment, which might motivate them to register for an account.
<br/>
<br/>

<strong>Example</strong>
<br/>

"🎁 Get 50 Free Spins! Install Now and deposit $20 to claim. Use code: SPIN50. T&Cs apply."</p> 



                </div>





            </div>

           
<div class="row">

<div class="col-md-8" style="margin-top:65px;">
                <h5 class="h5class"><strong>Player Retention</strong> </h5>
                <p>Leverage SureCLIQ's integrated tracking software to understand players' behaviour and preferences. Use this information to send personalized messages for promotions and bonuses. This will Ensure that you keep <strong>VIP players </strong>engaged with personalized messages with offers & special benefits.<br/>
<br/>

<strong>Example</strong>
<br/>

"We miss you! Here's $10 free credit to get back in the game. No deposit needed. Come play: link(abcde.com/live)"</p> 
          




                </div>


                <div class="col-md-4 image-container" style="display:block; text-align:center; margin-top:35px;" >
<img src="uploads/notifi.png" style="width: 70%;
    border-radius: 28px;     box-shadow: 4px 4px 8px 0px black;" />
                </div>
                






        
</div>


<div class="row">
          

<div class="col-md-4 image-container" style="display:block; text-align:center; margin-top:35px;" >
<img src="uploads/notificationx.png" style="width: 70%;    margin-top: 41px;
    border-radius: 28px;     box-shadow: 4px 4px 8px 0px black;" />
                </div>
     



                <div class="col-md-8" style="margin-top:65px;">
                <h5 class="h5class"><strong>Notifications</strong> </h5>
                <p>Keep your players updated by sending them reminders via SMS for <strong>upcoming future events</strong> which increases the chances of attracting more players.
          </p>
          <p>

          <strong>Example</strong>
          <br/>
"⚽️ Live In-Play: Man U vs Chelsea starts in 10 mins.abcde.com/live" 
</p>
          <p><strong >Apart from these SureCLIQ can send authentication, transcational messages like below
          </strong></p>
          <p>
"Your OTP for ABCD is 492736. Expires in 5 minutes."
<br/>
"Deposit of $50 successful. Your ABCD balance is now $120. Play now: abcd.com"</p>
                </div>
</div>
        
        </section>
      
      </div>

      <div id="testimonials" class="section wb">
        <div class="container">
            <div class="section-title text-center">
                <h3>Happy Clients</h3>
                <p class="lead">We thanks for all our awesome testimonials! There are hundreds of our happy customers!
                    <br>Let's see what others say about SureCLIQ!
                </p>
            </div><!-- end title -->

            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="testi-carousel owl-carousel owl-theme">
                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Boosted ROI!</h3>
                                <p class="lead">"SureCLIQ has revolutionized how we reach our audience. Their real-time
                                    tracking and multi-channel campaigns have boosted our ROI by over 15% in just
                                    months."</p>
                            </div>
                            <div class="testi-meta">
                                <h4>Alex Morgan<small>- Marketing Director</small></h4>
                            </div>
                            <!-- end testi-meta -->
                        </div>
                        <!-- end testimonial -->

                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Cost Efficiency!</h3>
                                <p class="lead">"The ability to target users across continents with localized precision
                                    is a game changer. Our campaign costs have significantly dropped while conversions
                                    soared."</p>
                            </div>
                            <div class="testi-meta">
                                <h4>Jacques Philips <small>- Designer</small></h4>
                            </div>
                            <!-- end testi-meta -->
                        </div>
                        <!-- end testimonial -->

                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Intuitive Dashboard!</h3>
                                <p class="lead">"The dashboard is incredibly intuitive, saving our team hours every
                                    week. The insights we get help us make smarter decisions faster." </p>
                            </div>
                            <div class="testi-meta">
                                <h4>Michael Chen <small>-CMO</small></h4>
                            </div>
                            <!-- end testi-meta -->
                        </div>
                        <!-- end testimonial -->
                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Scalable Platform!</h3>
                                <p class="lead">"From small batches to millions of users, SureCLIQ’s platform scales
                                    effortlessly. Their support team is responsive and truly understands our industry."
                                </p>
                            </div>
                            <div class="testi-meta">
                                <h4>Lara Singh <small>- Digital Entertainment</small></h4>
                            </div>
                            <!-- end testi-meta -->
                        </div>
                        <!-- end testimonial -->

                    </div><!-- end carousel -->
                </div><!-- end col -->
            </div><!-- end row -->
        </div><!-- end container -->
    </div><!-- end section -->


<?php require_once('footer.php'); ?>

<script>
  const slideElements = document.querySelectorAll('.slide-in-left, .slide-in-right');

  const observerOptions = {
    threshold: 0.15
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if(entry.isIntersecting){
        entry.target.classList.add('active');
      }
    });
  }, observerOptions);

  slideElements.forEach(el => {
    observer.observe(el);
  });
</script>


<script>
    // Get the image element
const image = document.getElementById('image');

// Function to change the image on mobile screens
function changeImage() {
  // Check if the screen width is less than or equal to 768px
  if (window.innerWidth <= 768) {
    // Add the mobile-image class to the image element
    image.src = 'uploads/banner3s.jpg';
  } else {
    // Remove the mobile-image class from the image element
    image.src = 'uploads/SureCLIQ11.jpg';
  }
}

// Call the function when the page loads
window.onload = changeImage;

// Call the function when the window is resized
window.addEventListener('resize', changeImage);

</script>