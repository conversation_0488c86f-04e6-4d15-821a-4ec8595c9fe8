# SureCLIQ Deployment Guide

## 🚀 Multiple Ways to Run the Application

### Option 1: PHP Built-in Server (Recommended for Development)

If you have PHP installed:

```bash
# Make the script executable
chmod +x run-local.sh

# Run the local server
./run-local.sh
```

Or manually:
```bash
php -S localhost:8000
```

**Access at**: http://localhost:8000

### Option 2: Docker (Recommended for Production)

#### Fix Docker Credential Issues First:

1. **Restart Docker Desktop**:
   - Close Docker Desktop completely
   - Restart Docker Desktop
   - Wait for it to fully start

2. **Reset Docker Credentials**:
   ```bash
   # Remove existing credentials
   rm ~/.docker/config.json
   
   # Or reset Docker Desktop to factory defaults
   ```

3. **Alternative Docker Commands**:
   ```bash
   # Try building without cache
   docker build --no-cache -t surecliq-app .
   
   # Or use docker compose
   docker compose up --build
   ```

#### If Docker Still Doesn't Work:

Try this simpler Dockerfile approach:

```dockerfile
FROM nginx:alpine
COPY . /usr/share/nginx/html
EXPOSE 80
```

### Option 3: Using XAMPP/WAMP/MAMP

1. Install XAMPP, WAMP, or MAMP
2. Copy the project folder to the `htdocs` directory
3. Start Apache
4. Access at: http://localhost/sureqli

### Option 4: Using Python HTTP Server (Static Files Only)

```bash
# Python 3
python3 -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

**Note**: This won't execute PHP, but you can view the HTML structure.

## 🔧 Troubleshooting Docker Issues

### Common Docker Problems:

1. **Docker Credential Error**:
   ```bash
   # Solution 1: Login to Docker Hub
   docker login
   
   # Solution 2: Use different base image
   # Edit Dockerfile to use: FROM nginx:alpine
   ```

2. **Docker Not Running**:
   ```bash
   # Check Docker status
   docker info
   
   # Start Docker Desktop manually
   ```

3. **Port Already in Use**:
   ```bash
   # Use different port
   docker run -p 8081:80 surecliq-app
   ```

## 📁 Project Structure

```
sureqli/
├── index.php                 # Homepage
├── digital-marketing.php     # New Digital Marketing page
├── solutions.php            # Solutions overview
├── header.php              # Navigation header
├── footer.php              # Footer
├── uploads/                # Images and assets
├── css/                   # Stylesheets
├── js/                    # JavaScript files
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Docker Compose
├── run-local.sh          # Local PHP server script
└── start.sh              # Docker startup script
```

## 🌐 Page URLs

Once running, access these pages:

- **Homepage**: `/` or `/index.php`
- **Digital Marketing**: `/digital-marketing.php`
- **Solutions**: `/solutions.php`
- **SMS Marketing**: `/sms-marketing.php`
- **WhatsApp Marketing**: `/whatsapp-marketing.php`
- **RCS Messaging**: `/rcs.php`

## ✅ Verification

To verify the Digital Marketing page is working:

1. Navigate to the Solutions page
2. You should see 4 service boxes (including the new Digital Marketing)
3. Click on "Digital Marketing" 
4. The page should load with:
   - Yellow hero section
   - Service buttons (SEO, PPC, SMO, ORM, Email Marketing, Google Analytics)
   - Digital marketing illustration
   - Content sections about digital marketing services

## 🎯 Next Steps

1. **Choose your preferred deployment method**
2. **Test the Digital Marketing page**
3. **Customize content as needed**
4. **Deploy to production server**

## 📞 Support

If you encounter issues:
1. Check the console for error messages
2. Verify all files are in the correct locations
3. Ensure proper file permissions
4. Test with the simplest deployment method first
