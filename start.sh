#!/bin/bash

echo "🚀 Starting SureCLIQ Application..."
echo "=================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

echo "✅ Docker is available and running"

# Build and run the application
echo "🔨 Building Docker image..."
docker build -t surecliq-app .

if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully"
    
    # Stop any existing container
    docker stop surecliq 2>/dev/null || true
    docker rm surecliq 2>/dev/null || true
    
    echo "🚀 Starting SureCLIQ container..."
    docker run -d -p 8080:80 --name surecliq surecliq-app
    
    if [ $? -eq 0 ]; then
        echo "✅ SureCLIQ is now running!"
        echo "🌐 Access your application at: http://localhost:8080"
        echo "📄 Digital Marketing page: http://localhost:8080/digital-marketing.php"
        echo ""
        echo "To stop the application, run: docker stop surecliq"
        echo "To view logs, run: docker logs surecliq"
    else
        echo "❌ Failed to start the container"
        exit 1
    fi
else
    echo "❌ Failed to build Docker image"
    exit 1
fi
