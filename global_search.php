
    <div id="support" class="section db">
        <div class="container">
            <div class="section-title text-center">
                <!-- <small>Our Awesome Clients</small> -->
                <h3>Global Reach</h3>
                <p class="lead">Our services extend across a diverse range of countries, enabling businesses to tap into
                    markets around the world with precision and confidence. From North America to Africa, Asia to South
                    America, we deliver localized marketing solutions tailored to each region's unique needs and
                    preferences.</p>
            </div><!-- end title -->



            <style>
                .scliql-container {
                    max-width: 900px;
                    width: 100%;
                    background: #f9f9f9;
                    padding: 30px 40px;
                    border-radius: 12px;
                    box-shadow: 0 4px 10px rgba(20, 33, 61, 0.1);
                }

                .scliql-title {
                    text-align: center;
                    color: #0077b6;
                    font-weight: 900;
                    font-size: 2.8rem;
                    margin-bottom: 10px;
                }

                .scliql-subtitle {
                    color: #0096c7;
                    font-weight: 700;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #00b4d8;
                    padding-bottom: 8px;
                }

                .scliql-marquee-wrapper {
                    overflow: hidden;
                    width: 100%;
                    /* border: 1px solid #cbd5e1; */
                    border-radius: 50px;
                    background-color: #000020;
                    margin-bottom: 20px;
                }

                .scliql-marquee {
                    display: flex;
                    white-space: nowrap;
                    will-change: transform;
                    align-items: center;
                }

                .scliql-country-card {
                    display: flex;
                    align-items: center;
                    background: white;
                    color: #000020;
                    font-weight: 600;
                    font-size: 20px;
                    padding: 10px 18px;
                    margin-right: 24px;
                    border-radius: 12px;
                    user-select: none;
                }

                .scliql-country-icon {
                    margin-right: 10px;
                    font-size: 1.6rem;
                }

                /* Animation for left to right */
                @keyframes scliql-marquee-left-to-right {
                    0% {
                        transform: translateX(-100%);
                    }

                    100% {
                        transform: translateX(100%);
                    }
                }

                /* Animation for right to left */
                @keyframes scliql-marquee-right-to-left {
                    0% {
                        transform: translateX(100%);
                    }

                    100% {
                        transform: translateX(-100%);
                    }
                }

                .scliql-left-to-right {
                    animation: scliql-marquee-left-to-right 28s linear infinite;
                }

                .scliql-right-to-left {
                    animation: scliql-marquee-right-to-left 28s linear infinite;
                }

                @media (max-width: 600px) {
                    .scliql-country-card {
                        min-width: 120px;
                        font-size: 1rem;
                        padding: 8px 14px;
                        margin-right: 16px;
                    }
                }
            </style>
            <!-- <div class="scliql-marquee-wrapper" aria-label="Countries scrolling left to right">
                <div class="scliql-marquee scliql-left-to-right" aria-hidden="true">
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇺🇸</span>USA</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇨🇦</span>Canada</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇮🇳</span>India</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇷🇺</span>Russia</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇰🇿</span>Kazakhstan</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇭🇰</span>Hong Kong</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇪🇸</span>Spain</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇵🇱</span>Poland</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇨🇭</span>Switzerland</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇧🇷</span>Brazil</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇨🇴</span>Colombia</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇨🇱</span>Chile</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇳🇬</span>Nigeria</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇿🇲</span>Zambia</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇹🇿</span>Tanzania</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇿🇦</span>South Africa</div>
                </div>
            </div>

            <div class="scliql-marquee-wrapper" aria-label="Countries scrolling right to left">
                <div class="scliql-marquee scliql-right-to-left" aria-hidden="true">
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇺🇸</span>USA</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇨🇦</span>Canada</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇮🇳</span>India</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇷🇺</span>Russia</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇰🇿</span>Kazakhstan</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇭🇰</span>Hong Kong</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇪🇸</span>Spain</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇵🇱</span>Poland</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇨🇭</span>Switzerland</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇧🇷</span>Brazil</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇨🇴</span>Colombia</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇨🇱</span>Chile</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇳🇬</span>Nigeria</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇿🇲</span>Zambia</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇹🇿</span>Tanzania</div>
                    <div class="scliql-country-card"><span class="scliql-country-icon">🇿🇦</span>South Africa</div>
                </div>
            </div> -->


<div class="scliql-marquee-wrapper" aria-label="Countries scrolling left to right">
  <div class="scliql-marquee scliql-left-to-right" aria-hidden="true">
    <div class="scliql-country-card"><img src="flags/us.png" alt="USA" class="scliql-country-icon" />USA</div>
    <div class="scliql-country-card"><img src="flags/ca.png" alt="Canada" class="scliql-country-icon" />Canada</div>
    <div class="scliql-country-card"><img src="flags/in.png" alt="India" class="scliql-country-icon" />India</div>
    <div class="scliql-country-card"><img src="flags/ru.png" alt="Russia" class="scliql-country-icon" />Russia</div>
    <div class="scliql-country-card"><img src="flags/kz.png" alt="Kazakhstan" class="scliql-country-icon" />Kazakhstan</div>
    <div class="scliql-country-card"><img src="flags/hk.png" alt="Hong Kong" class="scliql-country-icon" />Hong Kong</div>
    <div class="scliql-country-card"><img src="flags/es.png" alt="Spain" class="scliql-country-icon" />Spain</div>
    <div class="scliql-country-card"><img src="flags/pl.png" alt="Poland" class="scliql-country-icon" />Poland</div>
    <div class="scliql-country-card"><img src="flags/ch.png" alt="Switzerland" class="scliql-country-icon" />Switzerland</div>
    <div class="scliql-country-card"><img src="flags/br.png" alt="Brazil" class="scliql-country-icon" />Brazil</div>
    <div class="scliql-country-card"><img src="flags/co.png" alt="Colombia" class="scliql-country-icon" />Colombia</div>
    <div class="scliql-country-card"><img src="flags/cl.png" alt="Chile" class="scliql-country-icon" />Chile</div>
    <div class="scliql-country-card"><img src="flags/ng.png" alt="Nigeria" class="scliql-country-icon" />Nigeria</div>
    <div class="scliql-country-card"><img src="flags/zm.png" alt="Zambia" class="scliql-country-icon" />Zambia</div>
    <div class="scliql-country-card"><img src="flags/tz.png" alt="Tanzania" class="scliql-country-icon" />Tanzania</div>
    <div class="scliql-country-card"><img src="flags/za.png" alt="South Africa" class="scliql-country-icon" />South Africa</div>
  </div>
</div>

<div class="scliql-marquee-wrapper" aria-label="Countries scrolling right to left">
  <div class="scliql-marquee scliql-right-to-left" aria-hidden="true">
      <div class="scliql-country-card"><img src="flags/kz.png" alt="Kazakhstan" class="scliql-country-icon" />Kazakhstan</div>
      <div class="scliql-country-card"><img src="flags/tz.png" alt="Tanzania" class="scliql-country-icon" />Tanzania</div>
      <div class="scliql-country-card"><img src="flags/zm.png" alt="Zambia" class="scliql-country-icon" />Zambia</div>
      <div class="scliql-country-card"><img src="flags/ng.png" alt="Nigeria" class="scliql-country-icon" />Nigeria</div>
  <div class="scliql-country-card"><img src="flags/za.png" alt="South Africa" class="scliql-country-icon" />South Africa</div>
  <div class="scliql-country-card"><img src="flags/cl.png" alt="Chile" class="scliql-country-icon" />Chile</div>
  <div class="scliql-country-card"><img src="flags/co.png" alt="Colombia" class="scliql-country-icon" />Colombia</div>
  <div class="scliql-country-card"><img src="flags/br.png" alt="Brazil" class="scliql-country-icon" />Brazil</div>
  <div class="scliql-country-card"><img src="flags/ch.png" alt="Switzerland" class="scliql-country-icon" />Switzerland</div>
  <div class="scliql-country-card"><img src="flags/pl.png" alt="Poland" class="scliql-country-icon" />Poland</div>
  <div class="scliql-country-card"><img src="flags/es.png" alt="Spain" class="scliql-country-icon" />Spain</div>
  <div class="scliql-country-card"><img src="flags/hk.png" alt="Hong Kong" class="scliql-country-icon" />Hong Kong</div>
  <div class="scliql-country-card"><img src="flags/ru.png" alt="Russia" class="scliql-country-icon" />Russia</div>
  <div class="scliql-country-card"><img src="flags/in.png" alt="India" class="scliql-country-icon" />India</div>
  <div class="scliql-country-card"><img src="flags/ca.png" alt="Canada" class="scliql-country-icon" />Canada</div>
  <div class="scliql-country-card"><img src="flags/us.png" alt="USA" class="scliql-country-icon" />USA</div>

</div>
</div>






        </div><!-- end container -->
    </div><!-- end section -->

    <svg id="clouds1" class="hidden-xs" xmlns="http://www.w3.org/2000/svg" version="1.1" width="100%" height="100"
        viewBox="0 0 85 100" preserveAspectRatio="none">
        <path d="M-5 100 Q 0 20 5 100 Z
            M0 100 Q 5 0 10 100
            M5 100 Q 10 30 15 100
            M10 100 Q 15 10 20 100
            M15 100 Q 20 30 25 100
            M20 100 Q 25 -10 30 100
            M25 100 Q 30 10 35 100
            M30 100 Q 35 30 40 100
            M35 100 Q 40 10 45 100
            M40 100 Q 45 50 50 100
            M45 100 Q 50 20 55 100
            M50 100 Q 55 40 60 100
            M55 100 Q 60 60 65 100
            M60 100 Q 65 50 70 100
            M65 100 Q 70 20 75 100
            M70 100 Q 75 45 80 100
            M75 100 Q 80 30 85 100
            M80 100 Q 85 20 90 100
            M85 100 Q 90 50 95 100
            M90 100 Q 95 25 100 100
            M95 100 Q 100 15 105 100 Z">
        </path>
    </svg>
