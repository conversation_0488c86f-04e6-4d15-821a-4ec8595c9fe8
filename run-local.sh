#!/bin/bash

echo "🚀 Starting SureCLIQ Application (Local Development Server)..."
echo "============================================================"

# Check if PHP is installed
if ! command -v php &> /dev/null; then
    echo "❌ PHP is not installed. Please install PHP first."
    echo "On macOS: brew install php"
    echo "On Ubuntu: sudo apt-get install php"
    exit 1
fi

echo "✅ PHP is available"

# Get the current directory
CURRENT_DIR=$(pwd)

echo "📁 Serving files from: $CURRENT_DIR"
echo "🌐 Starting PHP development server..."
echo ""
echo "✅ SureCLIQ is now running!"
echo "🌐 Access your application at: http://localhost:8000"
echo "📄 Digital Marketing page: http://localhost:8000/digital-marketing.php"
echo "📄 Solutions page: http://localhost:8000/solutions.php"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start PHP built-in server
php -S localhost:8000
