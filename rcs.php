<?php $MENU='rcs'; require_once('header.php'); ?>


<style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');
      
        p{
        font-family: system-ui;
        text-align: justify;
        font-size:18px !important;

      }
        /* Banner styles */
        .banner {
            /* background-image: url(''); */
          display: flex;
          /* align-items: center; */
          color: black;
          margin-top: 120px;
          background-color: #ffde59;
        }
        .banner-text {
          /* max-width: 600px; */
          /* padding: 25px 30px; */
          border-radius: 12px;
            position: absolute;
            margin: 0px 60px;
            max-width: 70%;
    padding: 25px 58px;
    margin-top: 146px;
        }
        .banner-text h1 {
          margin: 0 0 15px;
          font-size: 80px;
          font-weight: 700;
          letter-spacing: 1.2px;
          color: black;

        }
        .banner-text p {
            font-size: 18px;
          font-weight: 400;
          line-height: 1.4;
          margin-left: 14px;
        }
      
        /* Container */
        .containerx {
          max-width: 85%;
          margin: 0 auto;
          padding: 60px 20px 100px;
        }
      
        /* Each section */
        section {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 90px;
          gap: 40px;
        }
      
        /* Alternate section direction */
        section.even {
          flex-direction: row-reverse;
        }
      
        .content {
          flex: 1 1 520px;
        }
        .content h2 {
          font-weight: 700;
          font-size: 2.8rem;
          margin-bottom: 22px;
          color: #fed557;
        }
        .content p {
          font-size: 1.25rem;
          margin-bottom: 20px;
          color: #444;
        }
        .content ul {
          list-style-type: disc;
          padding-left: 22px;
          color: #444;
          font-size: 18px;
        }
        .content ul li {
          margin-bottom: 14px;
        }
      
        /* Image container */
        .image-container {
          flex: 1 1 520px;
          text-align: center;
        }
        .image-container img {
          max-width: 100%;
          border-radius: 20px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
          transition: transform 0.3s ease;
          max-height: 420px;
          object-fit: cover;
        }
        .image-container img:hover {
          transform: scale(1.07);
        }
      
        /* Animation base and effect */
        .slide-in-left, .slide-in-right {
          opacity: 0;
          transform: translateX(0);
          transition: transform 0.9s ease, opacity 0.9s ease;
        }
        .slide-in-left {
          transform: translateX(-60px);
        }
        .slide-in-right {
          transform: translateX(60px);
        }
        .slide-in-left.active,
        .slide-in-right.active {
          opacity: 1;
          transform: translateX(0);
        }

        .banner img{
            width: 100%;
            height: 70%;
            margin: 0px auto !important;
        }
      
        /* Responsive for smaller devices */
        @media (max-width: 860px) {
          section {
            flex-direction: column !important;
            margin-bottom: 70px;
          }
          .content, .image-container {
            flex: 1 1 100%;
            padding: 10px 0;
          }
          .banner-text h1 {
            font-size: 55px;
            color: white;
          }
          .banner-text {
            padding: 0px ;
            color: white;

          }
          .banner-text p {
            font-size: 12px;
            color: white;

          }
          .banner{
            margin-top: 0px;
            color: white;

          }
          .banner img{
            width: 900px;
            height: 650px;
          }
        }
      
      
      </style>
         



    <!-- <div id="home" class="parallax first-section" data-stellar-background-ratio="0.4" style="background-image:url('uploads/parallax_12.jpg');"> -->
      <div id="home" class="parallax first-section" data-stellar-background-ratio="0.4" style="background-color:#fed557;">
        <div class="container">
            <div class="row">
                <div class="col-md-6 col-sm-12">
                    <div class="big-tagline">
                        <h2 style="color:black !important;">RCS Messaging</h2>
                        <p style="color:black !important;     margin-top: -25px;" class="lead"> Branded Messages for deeper connection</p>
                        <!-- <a href="https://my.surecliq.com/account/signup" class="btn btn-light btn-radius btn-brd ban-btn" style="border-radius: 50px;">Start Sending</a> -->
                        <a href="https://name.surecliq.com/console/#signup" class="btn btn-light btn-radius btn-brd ban-btn" style="border-radius: 50px;">Start Sending</a>
                    </div>
                </div>


                <div class="col-md-6 col-sm-12">
                  <img style="    width: 80%;
                  margin: auto;
                  margin-top: 50px;" src="uploads/SureCLIQ-short32.png" alt="" class="img-responsive">
              </div>

                <!-- <div class="app_iphone_02 wow slideInUp hidden-xs hidden-sm" data-wow-duration="1s"
                    data-wow-delay="0.5s">
                    <img src="uploads/rocket.png" alt="" class="img-responsive">
                </div> -->
            </div><!-- end row -->
        </div><!-- end container -->
    </div><!-- end section -->

    <svg id="clouds" class="hidden-xs" xmlns="http://www.w3.org/2000/svg" version="1.1" width="100%" height="100"
        viewBox="0 0 85 100" preserveAspectRatio="none">
        <path d="M-5 100 Q 0 20 5 100 Z
            M0 100 Q 5 0 10 100
            M5 100 Q 10 30 15 100
            M10 100 Q 15 10 20 100
            M15 100 Q 20 30 25 100
            M20 100 Q 25 -10 30 100
            M25 100 Q 30 10 35 100
            M30 100 Q 35 30 40 100
            M35 100 Q 40 10 45 100
            M40 100 Q 45 50 50 100
            M45 100 Q 50 20 55 100
            M50 100 Q 55 40 60 100
            M55 100 Q 60 60 65 100
            M60 100 Q 65 50 70 100
            M65 100 Q 70 20 75 100
            M70 100 Q 75 45 80 100
            M75 100 Q 80 30 85 100
            M80 100 Q 85 20 90 100
            M85 100 Q 90 50 95 100
            M90 100 Q 95 25 100 100
            M95 100 Q 100 15 105 100 Z">
        </path>
    </svg>














    <div class="containerx" role="main">

        <!-- Section 1: Introduction -->
        <section>
            <div class="content slide-in-left">
                <h2>What is RCS Messaging Marketing?</h2>
                <p>Rich Communication Services (RCS) messaging is revolutionizing the way businesses communicate with
                    customers through mobile devices. Often called the evolution of SMS, RCS combines the ubiquity of
                    traditional text messaging with the rich features of modern chat apps. This results in interactive,
                    visually appealing, and engaging messages that create robust customer experiences.</p>
                <p>This guide will explore everything you need to know about RCS marketing — from what it is and why it
                    matters to the most effective strategies, best practices, and inspiring use cases. Whether you're a
                    marketer, business owner, or communications professional, this resource will help you harness RCS to
                    boost engagement, conversion, and customer loyalty.</p>
            </div>
            <div class="image-container slide-in-right">
                <img src="uploads/rcs1.png"
                    alt="Person holding smartphone sending messages" />
            </div>
        </section>

        <!-- Section 2: Why Use SMS Marketing -->
        <section class="even">
            <div class="content slide-in-right">
                <h2>Why RCS Marketing Matters</h2>
    <p>Traditional SMS messaging is limited in functionality and can feel outdated to modern consumers who expect richer experiences. RCS marketing bridges this gap by enhancing business messaging with advanced features. Here's why RCS is a game changer for marketing:</p>
    <ul>
      <li><strong>Rich, Interactive Content:</strong> Send images, videos, carousels, suggested replies, and action buttons all within the messaging environment.</li>
      <li><strong>Ubiquity:</strong> Delivered through built-in messaging apps on Android phones and soon broader compatibility worldwide, RCS reaches billions without requiring customers to download an app.</li>
      <li><strong>Higher Engagement:</strong> Rich media and interactivity drive customer interaction and meaningful conversations.</li>
      <li><strong>Secure and Reliable:</strong> Supports end-to-end encryption in many implementations, providing privacy and trust.</li>
      <li><strong>Seamless Integration:</strong> Integrate RCS with chatbots, CRM systems, and marketing automation platforms.</li>
      <li><strong>Improved Branding:</strong> Businesses can display verified brand identity, including logos and colors, increasing trust and recognition.</li>
    </ul>
    <p>With these benefits, RCS opens new doors for marketers targeting mobile-first customers and aiming to deliver personalized, timely, and interactive messages.</p>
 
            </div>
            <div class="image-container slide-in-left">
                <img src="uploads/rcs2.png"
                    alt="Person working on marketing campaign laptop" />
            </div>
        </section>

        <!-- Section 3: Features of SMS Marketing -->
        <section>
            <div class="content slide-in-left">
                <h2>Key Features of RCS Messaging</h2>
                <p>To utilize RCS effectively, it’s important to understand its core capabilities and message types:</p>
                <ul>
                  <li><strong>Read Receipts and Typing Indicators:</strong> Understand when messages are delivered, read, and when customers are typing responses, fostering real-time conversations.</li>
                  <li><strong>Rich Media</strong>: Share images, videos, GIFs, animations, audio clips, and carousels to create immersive messaging experiences.</li>
                  <li><strong>Suggested Actions and Replies:</strong> Include buttons for actions like “Buy Now,” “Reserve,” “Call,” or suggested reply options to guide user responses and streamline interactions.</li>
                  <li><strong>Verified Sender Branding:</strong> Display your business name, logo, and brand colors in the chat, providing authenticity and reducing fraud risk.</li>
                  <li><strong>File Sharing:</strong> Send documents, brochures, and PDFs directly within the chat for quick access.</li>
                  <li><strong>Chatbots and Automation:</strong> Combine with AI and automation to provide 24/7 support and conversational commerce capabilities.</li>
                  <li><strong>Group Chats:</strong> Facilitate group interactions for events, deals, or community building (still growing in adoption).</li>
                </ul>
            </div>
            <div class="image-container slide-in-right">
                <img src="uploads/rcs3.png"
                    alt="Marketing dashboard showing SMS campaign statistics" />
            </div>
        </section>




        <div class="row">
            <section style="display: block !important; padding: 20px !important;">
                <h2 style="    font-weight: 700;
                font-size: 2.8rem;
                margin-bottom: 22px;
                color: #fed557;">Effective RCS Marketing Strategies</h2>
 <p>Below are proven strategies to maximize business impact with RCS messaging campaigns:</p>
 <h3>1. Interactive Product Showcases</h3>
 <p>Create rich carousels allowing customers to swipe through multiple products with images, prices, and call-to-action buttons for easy browsing and purchase directly from the chat.</p>
 <h3>2. Seamless Appointment Booking and Reservations</h3>
 <p>Use suggested action buttons for booking appointments, reserving tables, or scheduling consultations without leaving the messaging app, reducing friction and boosting conversion rates.</p>
 <h3>3. Personalized Offers and Coupons</h3>
 <p>Send customers tailored discounts and coupon codes with branded visuals and direct redemption buttons, encouraging immediate actions and repeat purchases.</p>
 <h3>4. Real-Time Customer Support</h3>
 <p>Leverage chatbots integrated with RCS to provide instant answers, troubleshoot issues with rich media guides, and escalate queries to human agents when needed, enhancing customer experience.</p>
 <h3>5. Event Invitations and RSVP Management</h3>
 <p>Deliver visually appealing invitations with rich media, offer RSVP buttons, and send automated reminders to boost event attendance and engagement.</p>
 <h3>6. Re-Engagement Campaigns</h3>
 <p>Reach out to inactive customers with appealing rich content and suggested replies to draw them back to your service or store, reigniting brand affinity.</p>
 <h3>7. Surveys and Feedback Collection</h3>
 <p>Send concise surveys with easy tap responses and multimedia support to gather valuable customer insights and improve your offerings.</p>

            </section>

            <div style="display: block; text-align: center;">
                <!-- <a href="https://my.surecliq.com/account/signup" target="_blank" style="    color: white; -->
                <a href="https://name.surecliq.com/console/#signup" target="_blank" style="    color: white;
    background-color: #fed557;
    border-radius: 50px;
    padding: 20px;
    font-weight: bold;" rel="noopener noreferrer">Get Started with RCS Messaging</a>
            </div>
        </div>


    </div>




    <div id="testimonials" class="section wb">
        <div class="container">
            <div class="section-title text-center">
                <h3>Happy Clients</h3>
                <p class="lead" style="text-align:center;">We thanks for all our awesome testimonials! There are hundreds of our happy customers!
                    <br>Let's see what others say about SureCLIQ!
                </p>
            </div><!-- end title -->

            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="testi-carousel owl-carousel owl-theme">
                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Boosted ROI!</h3>
                                <p class="lead">"SureCLIQ has revolutionized how we reach our audience. Their real-time
                                    tracking and multi-channel campaigns have boosted our ROI by over 15% in just
                                    months."</p>
                            </div>
                            <div class="testi-meta">
                                <h4>Alex Morgan<small>- Marketing Director</small></h4>
                            </div>
                            <!-- end testi-meta -->
                        </div>
                        <!-- end testimonial -->

                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Cost Efficiency!</h3>
                                <p class="lead">"The ability to target users across continents with localized precision
                                    is a game changer. Our campaign costs have significantly dropped while conversions
                                    soared."</p>
                            </div>
                            <div class="testi-meta">
                                <h4>Jacques Philips <small>- Designer</small></h4>
                            </div>
                            <!-- end testi-meta -->
                        </div>
                        <!-- end testimonial -->

                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Intuitive Dashboard!</h3>
                                <p class="lead">"The dashboard is incredibly intuitive, saving our team hours every
                                    week. The insights we get help us make smarter decisions faster." </p>
                            </div>
                            <div class="testi-meta">
                                <h4>Michael Chen <small>-CMO</small></h4>
                            </div>
                            <!-- end testi-meta -->
                        </div>
                        <!-- end testimonial -->
                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Scalable Platform!</h3>
                                <p class="lead">"From small batches to millions of users, SureCLIQ’s platform scales
                                    effortlessly. Their support team is responsive and truly understands our industry."
                                </p>
                            </div>
                            <div class="testi-meta">
                                <h4>Lara Singh <small>- Digital Entertainment</small></h4>
                            </div>
                            <!-- end testi-meta -->
                        </div>
                        <!-- end testimonial -->

                    </div><!-- end carousel -->
                </div><!-- end col -->
            </div><!-- end row -->
        </div><!-- end container -->
    </div><!-- end section -->


<?php require_once('footer.php'); ?>



<script>
    const slideElements = document.querySelectorAll('.slide-in-left, .slide-in-right');

    const observerOptions = {
        threshold: 0.15
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('active');
            }
        });
    }, observerOptions);

    slideElements.forEach(el => {
        observer.observe(el);
    });
</script>


<script>
    // Get the image element
const image = document.getElementById('image');

// Function to change the image on mobile screens
function changeImage() {
  // Check if the screen width is less than or equal to 768px
  if (window.innerWidth <= 768) {
    // Add the mobile-image class to the image element
    image.src = 'uploads/banner3s.jpg';
  } else {
    // Remove the mobile-image class from the image element
    image.src = 'uploads/SureCLIQ13.jpg';
  }
}

// Call the function when the page loads
window.onload = changeImage;

// Call the function when the window is resized
window.addEventListener('resize', changeImage);

</script>