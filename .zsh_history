tar --exclude=".next" --exclude=".git" -czf dist-nextjs.tar.gz -C . .
tar --exclude=".next" --exclude=".git" --exclude="dist-nextjs.tar.gz" -czf dist-nextjs.tar.gz -C . .
ls
          tar --exclude=".next" --exclude=".git" -czf dist-nextjs.tar.gz -C . .\

          tar --exclude=".next" --exclude=".git" -czf dist-nextjs.tar.gz -C . 
          tar --exclude=".next" --exclude=".git" -czf dist-nextjs.tar.gz -C ./ .
          tar --exclude=".next" --exclude=".git" -czf dist-nextjs.tar.gz -C ./
clear
          tar --exclude=".next" --exclude=".git" -czf dist-nextjs.tar.gz -C
          tar --exclude=".next" --exclude=".git" -czf dist-nextjs.tar.gz 
clear
ls
          tar --exclude=".next" --exclude=".git" -czf dist-nextjs.tar.gz -C . .\

tar --exclude=".next" --exclude=".git" --exclude="dist-nextjs.tar.gz" --warning=no-file-changed -czf dist-nextjs.tar.gz -C . .
tar --exclude=".next" --exclude=".git" --exclude="dist-nextjs.tar.gz" -czf dist-nextjs.tar.gz -C . .
clear
ssh -i ~/.ssh/id_rsa nabin@************
ssh root@198.38.88.197
clear
ssh -i ~/.ssh/id_rsa nabin@************
npx serve out
ssh -i ~/.ssh/id_rsa nabin@************
npm i
npm run build
npm serve out
clear
ls
clear
ls
clear
finder .
open .
cd
cd ~/.ssh/
open .
cd
cd /Users/<USER>/Library/Containers/com.docker.docker/
cd /Users/<USER>/Library/Containers/com.docker.docker/data/desktop/namespace/
cd /Users/<USER>/Library/Containers/
ls
cd /var/run/com.docker.socket
cd /var/run
ls
sudo find / -name "com.docker.socket" 2>/dev/null
lsof | grep com.docker.socket\

sudo find / -name "com.docker.socket" 2>/dev/null
cd
ifconfig
sudo npm i 
cd Desktop/chat/frontend/
npm i 
npm run dev 
clear
code .
npm run dev 
cd
cd Desktop 
ls
code frontend/
clear
npm run dev
cd frontend/
npm run dev 
npm i 
npm run dev
ifconfig
npm run dev
npm run dev
npm run dev
clear
npm run dev
npm run dev
npm run dev
lucide-react
npm i lucide-react
npm create vite@latest\

clear
npm run dev
npm run dev
clear
ls
clear
clear
npm run dev
npm run dev
cear
clear
npm run dev
ssh zypsie
clear
npm ru ndev
npm run dev
npm run dev 
clear
npm run dev
cd
clear
ls
cd WebstormProjects/kalkism-research/
npm run dev
npm i 
sudo npm i 
clear
npm run dev
clear
sudo npm run build
npm i 
npm install lucide-react
sudo npm run build
sudo npm i lucide-react
rm -rf node_modules/ package-lock.json
sudo rm -rf node_modules/ package-lock.json
npm i 
clear 
sudo npm i 
npm run build 
sudo npm run build 
clear
git pull
clear
ls
npm run dev
clear
npm run dev
ls -a
cd .gi t
cd .git/
cat config
git pull
cd ..
git pull -force
git pull --force
git stash
git pull 
clear
git pull 
clear
cd .
cd ..
cd RANI_BET/
npm run dev
npm run build
npm run start
npx serve@latest out
clear
ls
tar -cvf web.tar out/
clear
cd ~/.ssh/
ls
open .
clear
ls
cd ..
ssh zypsie 
clear
ls
cd WebstormProjects/RANI_BET/
ls
code .
ls
scp web.tar zypsie:./tmp
scp web.tar zypsie:~/
clear
scp web.tar zypsie:/home/<USER>/
scp web.tar zypsie1:~/tmp/
scp web.tar zypsie1:/home/<USER>/
tar -cvf web.tar out/
clear
ls
scp web.tar zypsie1:/home/<USER>/
tar -cvf web.tar.gz out/
scp web.tar.gz zypsie1:/home/<USER>/
scp web.tar.gz zypsie1:/home/<USER>/
npx serve@latest out
clear
rm package-lock.json
npm i 
clear
ls
npm run dev
clear
git checkout nabin 
git st
git checkout beast
git stash
git checkout beast
clear
git chekout beast
git checkout beast
sudo npm run dev
sudo npm i 
npm install jsonwebtoken bcryptjs cookie\
npm install -D @types/jsonwebtoken @types/bcryptjs @types/cookie
clear
npm run build
sudo npm run dev
sudo npm run build
clear
sudo npm run build 
clear
ls
sudo npm run build 
clear
git checkout mohan-dev
npm i 
sudo npm i 
sudo npm run build
npm run build
sudo npm run build
sudo npm run build
sudo npm run build
sudo npm run build
sudo npm run build
sudo npm run build
sudo npm run build
sudo npm run build
sudo npm run build
sudo npm run build
sudo npm run build
sudo npm run build
npm i 
sudo npm install framer-motion@latest
sudo npm install framer-motion@latest
sudo npm run build
rm -rf node_modules/ package-lock.json
sudo rm -rf node_modules/ package-lock.json
npm i 
npm run dev
sudo npm run dev
npm run builf
sudo npm run build
clear
git push origin mohan-dev:main --force
git checkout main
git checkout main
npm run dev
sudo npm run dev
git push origin mohan-dev:main --force
npm run dev
git checkout main\
git reset --hard mohan-dev  # Make main identical to your branch\
git push origin main --force       # Force update remote main
git pull
git checkout mohan-dev
git checkout nabin
git merge mohan-dev
sudo npm run build
clear
pbcopy < ~/.ssh/id_contabo_new 
ssh zypsie 
clear
ssh zypsie 
ssh zypsie1
clear
ls
ssh zypsie
ssh zypsie1
ifconfig
clear
ls
cd ..
cd kalkism-research/
git merge nabin
git merge nabin
git reset --hard 2
git reset --hard HEAD~2
git fetch
git pull
npm rub dev
npm run dev
sudo npm run dev
npm run build
npm i 
npm run build
sudo npm run build
cldar
sudo npm run build
sudo npm run build
sudo npm run build
sudo npm run build
sudo npm run build
sudo npm run build
npm install framer-motion
npm install framer-motion@latest
git checkout nabin
sudo npm run build
sudo npm run build
git checkout nabin
git merge mohan-dev
git push
ssh zypsie1
ssh zypsie
clear
ssh zypsie
ssh zypsie
ssh zypsie
ssh zypsie
cd WebstormProjects/kalkism-research/
tar -cvf web.tar.gz public/
scp web.tar.gz zypsie:/home/<USER>/
npm run dev
sudo npm run dev
npm i cloudinary
sudo npm run dev
sudo npm run build
sudo npm run build
npm run dev
sudo npm run dev
cd public/
ls
rm -rf uploads/
cd ..
npm i bcrypt
npm i nodemailer
npm i nodemailer
cd WebstormProjects/gold-server/
npm i --save-dev @types/ nodemaile
npm i --save-dev @types/ nodemailer
sudo npm i --save-dev @types/ nodemaile
sudo npm i --save-dev @types/nodemailer
clear
ls
npm i --save-dev @types/ nodemaile
npm i --save-dev @types/ nodemailer
npm i --save-dev @types/ nodemaile
ifconfig 
ssh zypsie
ssh zypsie1
ssh zypsie1
clear
ls
ssh zypsie
npm i 
npm i 
sudo npm i 
cd admin
npm i 
npm install husky --save-dev
clear
npm i husky 
npm i 
git ac "changes"
cd ..
cd kalkisena/
npm i 
npm i 
ssh zypsie1
ssh zypsie
clear
ssh zypsie
cd NEWS-APPLICATION/
cd server
ls
cd ..
tar -cvf server.tar server/
scp server.tar zypsie1:/home/<USER>
ssh zypsie 1
ssh zypsie1
ssh zypsie
ssh zypsie1
clear
ssh zypsie1
clear
cd
ls
ssh zypsie1
ssh zypsie1
ssh zypsie
cd
cd WebstormProjects/kalkism-research/
ls
cd public/
ls
cd ..
ls
tar -cvf web.tar.gz public/
scp web.tar.gz zypsie:/home/<USER>/
tar -cvf web.tar.gz public/
scp web.tar.gz zypsie:/home/<USER>/
npm i
ssh zypsie
pbcopy < ~/.ssh/id_contabo_new 
clear
pbcopy < ~/.ssh/id_contabo_new
ssh zypsie
ssh zypsie
ssh zypsie
ssh zypsie
ssh zypsie
ssh zypsie1
ssh zypsie1
ssh zypsie
npm i axios
ssh zypsie
ssh zypsie1
pbcopy < ~/.ssh/id_contabo_new
pbcopy < ~/.ssh/id_contabo2
ssh zypsie1
ssh zypsie
ssh zypsie1
ssh zypsie1
ssh zypsie1=
ssh zypsie
ssh zypsie1
ssh zypsie
ssh zypsie1
ssh zypsie
clear
clear
ssh zypsie
ssh zypsie1
clear
ssh zypsie
ssh zypsie
ckear
clear
ssh zypsie
cd .ssh/config
cat .ssh/config
pbcopy < ~/.ssh/id_contabo_new 
pbcopy < ~/.ssh/id_contabo_new
pbcopy < ~/.ssh/id_contabo_new
ssh zypsie
ssh zypsie
ssh zypsie
git push
ssh zypsie
\
htpasswd -nbBC 10 admin gdK6OBtRc0YqbPF72bdWjwU
ssh zypsie
ssh zyspie1
ssh zyspie1
ssh zypsie1
ssh zypsie 
brew tap hashicorp/tap\
brew install hashicorp/tap/terraform
ssh zypsie 
ssh zypsie1
npm i grafana/faro-react
cd ..
cd client/
npm i grafana/faro-web-tracing
npm install @grafana/faro-react\
npm install @grafana/faro-web-tracing
sudo npm install @grafana/faro-react\
sudo npm install @grafana/faro-web-tracing
sudo npm install -f @grafana/faro-react\
sudo npm install -f @grafana/faro-web-tracing
npm run dev
npm i
npm i -f
sudo npm i -f 
npm run dev
git checkout man
git checkout main
git checkout main
git pull
git merge shishir
clear
git push
ssh zypsie1
npm i @paypal/checkout-server-sdk
npm i --save-dev @types/paypal__checkout-server-sdk
npm i axios
npm i @paypal/react-paypal-js
npm run dev
npx shadcn@2.3.0 add "https://v0.dev/chat/b/b_QWTmK1RFEqG?token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..nnYeWgFf-VSg8ZEZ.r3ObSxbH1b82KEZbNjbyXCgHh1rVWNkcyKAKcEy5f9H_iaitlcFqnkrJ1vc.fISpM-KZS6N75Qf625Jl9g"
npm run dev
npm run dev
npm i
cd a=..
cd..
cd c
cd ..
cd client/
npm run dev
npm run dev
ssh zypsie
clear
ssh zypsie
ssh zypsie1
ssh zypsie
npm run dev
cd ..
cd client/
npm run dev
npm run dev
npm run dev
npm run dev
ssh zypsie1
ssh zypsie
ssh zypsie1
brew tap hashicorp/tap\
brew install hashicorp/tap/terraform
terraform version
mkdir Terraform
echo "# Terraform" >> README.md\
git init\
git add README.md\
git commit -m "first commit"\
git branch -M main\
git remote <NAME_EMAIL>:webstudionepal1/Terraform.git\
git push -u origin main
ls
mv .git/ Terraform/
cd Terraform
cd Terraform
terraform init
terraform plan
terraform init
ls ~/.ssh/
ls
terraform plan
terraform init
terraform init
terraform plan
ssh zypsie
ssh zypsie
./import_remote_containers.sh --server ************* --user nabin --port 22 --key ~/.ssh/id_contabo_new
./direct_import.sh
./manual_import.sh cadvisor 5fccc7957b72
ssh zypsie
./manual_import.sh bhansa_mart 892027d336b8
./import_direct.sh
./test_ssh.sh
./standalone_import.sh 892027d336b8
./create_container_config.sh bhansa-mart
./manual_direct_import.sh bhansa_mart 91c90c30465b
./standalone_import.sh 91c90c30465b
terraform init
terraform plan
terraform init
cd ..
clear
ssh zypsie
terraform init
terraform init
terraform plan
clear
./import_containers.sh
cd .ssh/
cat config
ssh zypsie
ssh zypsie1
ssh zypsie
tsh login
ssh zypsie
ssh zypsie
ssh zypsie1
npm i 
npm i firebase-admin
./run-contabo.sh contabo.yml
./run-contabo.sh test-ssh.yml
./run-all-contabo.sh
./vault-password.sh
./vault-password.sh
./vault-password.sh
./vault-password.sh
./run-all-contabo.sh
./run-all-contabo.sh
./run-all-contabo.sh
./manage-vault.sh --test
./update-contabo2-firewall.sh
clear
/bin/bash /Users/<USER>/WebstormProjects/Ansible/run-contabo1.sh
ssh zypsie
ssh zypsie1
cd
ls
cd WebstormProjects/Ansible/
# Test connectivity\
./check-servers.sh\
\
# Try running a simple role\
./run-role.sh contabo1 base --check  # Dry run
find . -type f -name "*.yml" | sort
find . -type f -name "*.sh" | sort
./setup-vault.sh
./setup-vault.sh
./setup-vault.sh
./setup-vault.sh
./test-connectivity.sh
\
# Or run specific configurations\
./run-contabo1.sh
clear
./setup-vault.sh
./test-connectivity.sh
./run-contabo1.sh
./run-contabo1.sh
ansible-playbook playbooks/docker.yml -i inventories/dev/hosts -e "target=all" --check
./setup-vault.sh
ansible-playbook playbooks/docker.yml -i inventories/dev/hosts -e "target=all" --check
./setup-vault.sh
ansible-playbook playbooks/docker.yml -i inventories/dev/hosts -e "target=all" --check
./setup-vault.sh
./run-contabo1.sh
./run-contabo2.sh
./deploy-project.sh contabo1 myproject nextjs
ssh zypsie1
ssh zypsie
./setup-docker-logging.sh -p zypsie
ansible-playbook -i inventories/dev playbooks/docker.yml
./setup-docker-logging.sh -p zypsie
./remove-docker-compose-v1.sh
ansible-playbook -i inventories/dev playbooks/docker.yml
./setup-docker-logging.sh -p zypsie
ssh zypsie
 ansible-playbook playbooks/docker_logging.yml -i inventories/dev/hosts
ssh zypsie
ssh nabin@************* -i ~/.ssh/id_contabo_new "docker logs loki"\
ssh nabin@************* -i ~/.ssh/id_contabo_new "docker logs prometheus"\
ssh nabin@************* -i ~/.ssh/id_contabo_new "docker logs promtail"
ssh zypsie
ssh zypsie1
cd WebstormProjects/Ansible/
ansible contabo2 -i inventories/dev/hosts -m copy -a "src=hotel-docker-compose.yml dest=/home/<USER>/hotel-mongodb/docker-compose.yml mode=0644"
ansible contabo2 -i inventories/dev/hosts -m copy -a "src=hotel-init-mongo.sh dest=/home/<USER>/hotel-mongodb/init-mongo.sh mode=0755"
 ansible contabo2 -i inventories/dev/hosts -m shell -a "docker exec -it hotel1 mongosh -u root -p kKo0K4LtUxIkRlO --eval 'rs.status()'"
ansible contabo2 -i inventories/dev/hosts -m shell -a "docker exec -it hotel1 mongosh -u root -p kKo0K4LtUxIkRlO --eval 'db.auth('root', 'kKo0K4LtUxIkRlO')'"
ansible contabo2 -i inventories/dev/hosts -m shell -a "docker exec -it hotel1 mongosh -u root -p kKo0K4LtUxIkRlO --eval 'db.runCommand({ isMaster: 1 })'"
ansible contabo2 -i inventories/dev/hosts -m shell -a "docker exec -it hotel1 mongosh -u root -p kKo0K4LtUxIkRlO --eval 'db.adminCommand({ listDatabases: 1 })'"
ansible contabo2 -i inventories/dev/hosts -m shell -a "docker exec -it hotel1 mongosh -u root -p kKo0K4LtUxIkRlO --eval 'db.auth('root', 'kKo0K4LtUxIkRlO')'"
\:q
./setup-swap.sh
ssh zypsie
ssh zypsie1
ssh zypsie
ssh zypsie1
curl "https://global-api-sandbox.afterpay.com/v2/configuration" \\
  -H 'Authorization: Basic MTEzMzU3Ojg5NmZkN2MxNTZmNDg2YWUyM2UwZDVjYmZmNjg4ZDAwMTFmNjkyNWQ2Njc1Y2Y3MjQyN2VjOWE3ZWRmOWI0OTcwNDZiZGM4MzIwNzA4MWVhYzgxMjNlYjVhMDdiNzg0NzhlOTc4OTMwZTZmYmY4MTgyMTU4MzQwMjUzYTdjYzY=\
\
'
curl -X POST "https://merchant-auth-sandbox.afterpay.com/v2/oauth2/token" \\
-H "Content-Type: application/x-www-form-urlencoded" \\
-d "grant_type=client_credentials&client_id=113357&client_secret=896fd7c156f486ae23e0d5cbff688d0011f6925d6675cf72427ec9a7edf9b497046bdc83207081eac8123eb5a07b78478e978930e6fbf8182158340253a7cc6&scope=merchant_api_v2"
curl -X POST "https://merchant-auth-sandbox.afterpay.com/v2/oauth2/token" \\
-H "Content-Type: application/x-www-form-urlencoded" \\
-d "grant_type=client_credentials&client_id=113357&client_secret=896fd7c156f486ae23e0d5cbff688d0011f6925d6675cf72427ec9a7edf9b497046bdc83207081eac8123eb5a07b78478e978930e6fbf8182158340253a7cc6&scope=merchant_api_v2"
curl -X POST "https://merchant-auth-sandbox.afterpay.com/v2/oauth2/token" \\
-H "Content-Type: application/x-www-form-urlencoded" \\
-d "grant_type=client_credentials&client_id=113357&client_secret=896fd7c156f486ae23e0d5cbff688d0011f6925d6675cf72427ec9a7edf9b497046bdc83207081eac8123eb5a07b78478e978930e6fbf8182158340253a7cc6&scope=merchant_api_v2"
curl -X POST "https://merchant-auth-sandbox.afterpay.com/v2/oauth2/token" \\
-H "Content-Type: application/x-www-form-urlencoded" \\
-d "grant_type=client_credentials&client_id=113357&client_secret=896fd7c156f486ae23e0d5cbff688d0011f6925d6675cf72427ec9a7edf9b497046bdc83207081eac8123eb5a07b78478e978930e6fbf8182158340253a7cc6&scope=merchant_api_v2"
curl -X POST "https://merchant-auth-sandbox.afterpay.com/v2/oauth2/token" \\
-H "Content-Type: application/x-www-form-urlencoded" \\
-d "grant_type=client_credentials&client_id=113357&client_secret=896fd7c156f486ae23e0d5cbff688d0011f6925d6675cf72427ec9a7edf9b497046bdc83207081eac8123eb5a07b78478e978930e6fbf8182158340253a7cc6&scope=merchant_api_v2"
ssh zypsie
ssh zypsie1
npm run dev
ssh zypsie1
ssh zypsie1
ssh zypsie1
ssh nabin@************ -p 72
ssh zypsie1
ssh zypsie
ssh zypsie1
ssh zypsie1
sudo vi .ssh/config 
ssh zypsie1
ssh zypsie
cd WebstormProjects/Ansible/
./scripts/setup-docker-logging.sh
./scripts/setup-docker-logging.sh
ssh zypsie1
./scripts/setup-docker-logging.sh
ansible-playbook playbooks/server_config.yml -e "target=contabo1"
ssh zypsie1
ssh zypsie
ansible-playbook playbooks/docker_logging.yml -i inventories/dev/hosts -e "target=contabo1,contabo2"
ssh zypsie
ssh zypsie1
 ansible-playbook playbooks/docker_logging.yml -i inventories/dev/hosts -e "target=contabo1,contabo2"
ansible contabo1 -i inventories/dev/hosts -m shell -a "docker logs otel-collector 2>&1 | tail -n 20"
ssh zypsie1
ssh zypsie
ssh zypsie1
ssh zypsie
ssh zypsie1
ssh zypsie
ssh zypsie1
cat ~/.ssh/config
ssh zypsie
cat ~/.ssh/id_contabo2 
pbcopy < ~/.ssh/id_contabo2
ssh zypsie1
clear
ssh zypsie1
clear
ssh
ssh zypsie1
ssh zypsie
ssh zypsie
ssh zypsie1
ssh zypsie1
pbcopy < ~/.ssh/id_contabo2
ssh zypsie 
ssh zypsie1
ssh zypsie
ssh zypsie1
clear
ls
clear
ssh zypsie
clear
ssh zypsie
celar
clear
ssh zypsie
cd admin/
npm run dev
npm i 
npm run dev
ssh zypsie1
pbcopy < ~/.ssh/id_contabo_new
ssh zypsie
npm run dev
npm run dev 
ls
cd admin/
npm run dev
npm i date-fns
npm run dev
ssh zypsie
npm run dev
cd admin/
npm run dev
ifconfig
nom run dec
nom run dev
npm run dev
cd admin
npm run dev
npm run dev
npm run build
git push 
git push
npm run dev
npm run dev
cd admin 
npm run dev
ssh zypsie1
ssh zypsie
ssh zypsie1
ssh zypsie1
ssh zypsie1
ssh zypsie
ssh zypsie1
ssh zypsie1
ssh zypsie
ssh zypsie
ssh zypsie
npm run dev
npm i 
npm update @nextui-org/system @nextui-org/card
npm update
rm -rf package-lock.json
npm update @nextui-org/system @nextui-org/card
npm install @nextui-org/theme@latest @nextui-org/system@latest @nextui-org/card@latest
sudo npm install @nextui-org/theme@latest @nextui-org/system@latest @nextui-org/card@latest
npm i 
sudo npm i 
sudo npm run dev
./scripts/run-populate.sh
./scripts/run-populate.sh
 npm run populate-reports\

npm run populate-reports
npm run populate-reports
npm run populate-guests-prod
cd admin
npm run dev
npm run dev
npm run dev
cd admin
npm run build
cd admin
npm run dev
npm run dev
cd admin 
npm run dev
cd admin/
npm run dev
npm run dev
cd admin/
npm run dev
cd admin/
npm run dev
npx @backstage/create-app@latest
cd backstage/
npm i
brew install k6
ls
cat package.json
npm install\

cd
cd WebstormProjects/SchoolServerGlobal/
k6 load-test.js 
k6 run load-test.js
cd ..
cd HospitalManagementSystem/
cd ..
cd HospitalServer/
ssh zypsie1
k6 run tests/k6/spike-test.js
k6 run tests/k6/spike-test.js
k6 run load-test.js
k6 run load-test.js
k6 run load-test.js
k6 run load-test.js
k6 run tests/k6/load-tests.js
k6 run tests/k6/load-tests.js
k6 run tests/k6/load-tests.js
./tests/k6/run-all-tests.sh
k6 run tests/k6/spike-test.js
k6 run tests/k6/spike-test.js
clear
k6 run tests/k6/spike-test.js
k6 run tests/k6/spike-test.js
ssh zypsie1
ssh zypsie
ssh **************
ssh **************
ssh **************
ssh root@**************
ssh nabin@**************
ssh root@**************
ssh root@**************
ssh root@**************
ssh root@**************
ssh root@**************
ssh nabin@**************
ssh-keygen -t ed25519 -C "<EMAIL>"
ssh-keygen -t ed25519 -C "<EMAIL>"
ssh-copy-id -i ~/.ssh/id_rsa.pub nabin@**************
ls /.ssh
ls -la ~/.ssh
ssh-keygen -t ed25519 -C "<EMAIL>"
ssh-keygen -t ed25519 -C "<EMAIL>"
ssh-copy-id -i ~/.ssh/prodserver.pub nabin@**************
ssh -i /Users/<USER>/.ssh/prodserver 'nabin@**************'
ssh -i /Users/<USER>/.ssh/prodserver 'nabin@**************'
vi ~/.ssh/config
ssh zypsieprod
ssh zypsie
sudo cp ~/.ssh/prodserver ~/Desktop/
sudo cp ~/.ssh/prodserver.pub ~/Desktop/
ssh zypsieprod
ssh -p 72 nabin@**************
ssh -p 72 nabin@**************
ssh -p 72 nabin@**************
ssh -p 72 nabin@**************
docker ps
ssh zypsieprod
sudo vi .ssh/config 
sudo vi ~/.ssh/config
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
pbcopy < ~/.ssh/prodserver 
clear
ssh zypsieprod
scp web.tar.gz zypsieprod:/home/<USER>/
scp README.md zypsieprod:/home/<USER>/
ssh zypsie1
ansible-vault encrypt group_vars/production/vault.yml
ansible-vault encrypt group_vars/production/vault.yml
ansible-vault encrypt group_vars/production/vault.yml
ansible-vault edit group_vars/production/vault.yml
ansible-vault edit group_vars/production/vault.yml
ansible-vault edit group_vars/production/vault.yml
ansible-vault edit group_vars/production/vault_new.yml
$ ansible-playbook install_mongodb_docker_cluster.yml -v\

 ansible-playbook install_mongodb_docker_cluster.yml -v
ansible-playbook install_mongodb_docker_cluster.yml -v
ssh zypsieprod
ansible-playbook install_mongodb_docker_cluster.yml -e "mongodb_cluster_name=indiaSchool mongodb_root_password=WSq9vdm9O5GI9v3"
cd
cd WebstormProjects/AnsibleProd/
ansible-playbook install_mongodb_docker_cluster.yml -e "mongodb_cluster_name=indiaSchool mongodb_root_password=WSq9vdm9O5GI9v3"
clear
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ansible-playbook create_nginx_site_interactive.yml
ansible-playbook create_nginx_site_interactive.yml
git checkout prod
ssh zypsie
ssh zypsieprod
ssh zypsieprod
ssh zypsie
ssh zypsie
ssh zypsie1
cd 
cd WebstormProjects/SchoolServerGlobal/
k6 load-test.js
k6 run load-test.js
k6 run load-test.js
clear
k6 run load-test.js
clear
k6 run load-test.js
k6 run load-test.js
clear
k6 run load-test.js
clear
k6 run spike-test.js
k6 run spike-test.js
cd ..
dcd
cd
cd backstage/
ls
npm i 
ssh zypsie
ssh zypsieprod
cd server
cd main
k6 run spike-test.js
ls
cd
cd
cd WebstormProjects/SchoolServerGlobal/
k6 spike-test.js
k6 run spike-test.js
k6 run spike-test.js
clear
k6 run spike-test.js
k6 run load-test.js
ssh zypsieprod
ifconfig
ssh zypsieprod -L 1999:localhost:1999
ifconfig | grep "inet " | grep -v 127.0.0.1
ifconfig
ssh -L 19999:localhost:19999 zypsieprod
ssh -N -L 19999:localhost:19999 zypsieprod
cd
cd WebstormProjects/AnsibleProd/
cd /Users/<USER>/WebstormProjects/AnsibleProd\
mv group_vars/production/vault.yml group_vars/production/vault.yml.old\
touch group_vars/production/vault.yml
ansible-vault edit group_vars/production/vault.yml
ansible-vault create group_vars/production/vault.yml
ansible-playbook setup_selinux.yml -v
ansible-playbook setup_selinux.yml -v
clear
ssh zypsieprod
ansible-playbook setup_selinux.yml -v
clear
ssh zypsieprod
sudo ausearch -m AVC,USER_AVC -ts recent\

ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh **************
ssh -p 72 **************
ssh **************
ssh -p 72 **************
ssh **************
ssh **************
ssh zypsieprod
clear
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
clear
clear
ssh zypsieprod
clear
clear
ls
ssh zypsie
ssh zypsie
ssh zypsieProd
ssh zypsieprod
ssh zypsie
ssh zypsie1
ssh zypsie
ansible-playbook setup_selinux.yml -v
ansible-vault edit group_vars/production/vault.yml
clear
ansible-playbook -i inventory/hosts apply_selinux_enforcing_final.yml
ssh zypsieprod
clear
ssh zypsie
clear
ls
ssh zypsieprod
curl -H "Host: test.webstudiomatrix.com" http://**************
sudo systemctl status nginx
curl -I http://**************
curl -I http://test.webstudiomatrix.com
curl http://test.webstudiomatrix.com
curl http://test.webstudiomatrix.com
curl https://test.webstudiomatrix.com
curl https://tester.webstudiomatrix.com
curl http://test.webstudiomatrix.com
curl http://testwe.webstudiomatrix.com
curl http://tester.webstudiomatrix.com
curl https://tester.webstudiomatrix.com
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh zypsieprod
ssh nabin@**************
ssh -i /Users/<USER>/.ssh/prodserver 'nabin@**************'
ssh zypsieprod
ssh zypsieprod
ansible-playbook -i inventory/hosts set_selinux_enforcing.yml
wxit
exigt
exit
: 1747575331:0;cd ..
: 1747575336:0;cd AnsibleProd/
: 1747575360:0;ansible-playbook create_nginx_site_interactive.yml
: 1747575373:0;ansible-playbook create_nginx_site_with_ssl.yml 
: 1747575412:0;./execute_nginx_site_config.yml 
: 1747575564:0;./create_nginx_config.sh
: 1747575630:0;./create_nginx_config.sh
: 1747575641:0;./create_nginx_config.sh
: 1747587003:0;ansible-playbook playbooks/setup/server_setup.yml 
: 1747587412:0;ansible-playbook playbooks/setup/server_setup.yml
: 1747587611:0;ansible-playbook playbooks/setup/server_setup.yml
: 1747587758:0;ssh zypsieprod
: 1747647503:0;ssh zypsie1
: 1747657663:0;clear
npm i --save-dev
npm i --save-dev http-proxy-middleware
: 1747662231:0;pbcopy < ~/.ssh/id_contabo_new 
: 1747663363:0;cat ~/.ssh/config
: 1747663375:0;pbcopy < ~/.ssh/id_contabo_new
: 1747664281:0;pbcopy < ~/.ssh/id_contabo2
: 1747664354:0;clear
: 1747664356:0;ls
: 1747664368:0;ssh zypsie
: 1747664531:0;ssh zypsie1
npm run dev
sudo npm run dev
cd admin 
npm i 
npm run dev
sudo npm i 
npm run build
npm run build
npm run build
: 1747672290:0;ssh zypsie1
: 1747672382:0;cat ~/.ssh/config | grep -A 5 "Host zypsieprod"
: 1747672384:0;ls -l ~/.ssh/prodserver
: 1747672438:0;ssh -N -L 8080:localhost:9105 zypsieprod
: 1747672743:0;ssh -N -R 9105:localhost:8085 zypsieprod
: 1747672793:0;ssh -N -R 8085:localhost:9105 zypsieprod
: 1747672852:0;ssh -N -R 9105:localhost:8080 zypsieprod
: 1747672884:0;ssh -N -R 8080:localhost:9105 zypsieprod
: 1747672929:0;ssh -N -R 8080:localhost:9105 zypsie1
: 1747672950:0;ssh -N -R 9105:localhost:8080 zypsie1
npm run dev
npm run dev
cd admin 
npm run dev
: 1747675162:0;ssh zypsie1
: 1747675247:0;ssh -N -R 8085:localhost:8081 zypsie1
: 1747678372:0;ssh zypsie1
: 1747678934:0;ssh zypsie1
: 1747712874:0;ssh -N -R 8085:localhost:8081 zypsie1
: 1747712900:0;ssh -N -R 9105:localhost:8080 zypsie1
cd admin
npm run dev
cd scripts
ls
cd ..
ls
./install_nginx_with_ssl.sh
: 1747720162:0;./install_nginx_with_ssl.sh 
: 1747720169:0;sudo ./install_nginx_with_ssl.sh 
: 1747720225:0;ssh zypsieprod
: 1747720292:0;ssh zypsieprod
: 1747724441:0;ssh zypsie1
npm run dev
git add .
git status
git commit -m "fix "
git push
git checkout prakash
git pull
: 1747847637:0;ssh zypsie1
: 1747847664:0;ssh -N -R 9105:localhost:8080 zypsie1
: 1747851631:0;ssh -N -R 9105:localhost:8080 zypsie1
: 1747851771:0;ssh -N -R 9105:localhost:8080 zypsie1
: 1747851781:0;ssh zypsie1
: 1747851910:0;ssh -N -R 9105:localhost:8080 zypsie1
: 1747851941:0;ssh -N -R 8080:localhost:9105 zypsie1
: 1747851945:0;ssh -N -R 9105:localhost:8080 zypsie1
: 1747852034:0;ssh zypsie1
: 1747852210:0;ssh -N -R 9105:localhost:8080 zypsie1
npm ru dev
npm run dev
npm run build 
git add .
git push
git pull
git push
git push 
npm run dev
npom run i 
npm i 
npm run dev
git add .
git push 
clear
: 1747917059:0;ssh zypsie
: 1747917077:0;ssh zypsie1
npm run dev
: 1747928535:0;ssh zypsie1
: 1747974316:0;celar
: 1747974317:0;clear
npm run de
npm run dev
./create_mongodb_cluster.sh
./create_mongodb_cluster.sh
./install_nginx_with_ssl.sh
sudo ./install_nginx_with_ssl.sh
./create_nginx_config.sh 
./create_nginx_config.sh 
: 1748004620:0;ssh zypsie1
git checkout prod
: 1748072194:0;clear
: 1748072938:0;ssh zypsieprod
: 1748072990:0;ssh zypsie
: 1748072999:0;ssh zypsie1
git push 
: 1748154320:0;clear
: 1748154342:0;ssh root@*************
: 1748154345:0;ssh root@*************
: 1748154448:0;ssh root@*************
: 1748154568:0;ssh nabin@*************
: 1748155495:0;ssh nabin@*************
: 1748156631:0;ssh nabin@*************
: 1748156708:0;clear
: 1748156714:0;ssh nabin@*************
: 1748158583:0;ssh nabin@192.168.0.10
: 1748158621:0;ssh nabin@*************
: 1748158845:0;ssh nabin@192.168.0.10
: 1748158995:0;ssh nabin@*************
: 1748158999:0;ssh nabin@192.168.0.10
: 1748159250:0;ssh nabin@*************
: 1748159261:0;ssh nabin@192.168.1.66
: 1748167282:0;ssh zypsie1
: 1748169080:0;ssh zypsie1
npm run dev
npm run de
npm run dev
npm run dev
: 1748177458:0;clear
: 1748177464:0;docker ps
: 1748177467:0;ssh zypsie1
npm run dev
: 1748227293:0;clear
npm run dev
: 1748251738:0;ssh zypsie1
npm run dev
npm run build 
npm run build
npm run dev
: 1748418866:0;ssh zypsie1
cd admin
npm i firebase
npm run build
npm run dev
: 1748493702:0;cd 
: 1748493706:0;cd .ssh/
: 1748493708:0;ls
npm run dev
npm run build
git push
npm run build
: 1748842664:0;clear
: 1748842721:0;ssh sCliQVes@72.21.24.149
: 1748842752:0;ssh -p 9001 sCliQVes@72.21.24.149
: 1748843250:0;ssh sCliQVes@72.21.24.149
: 1748843260:0;ssh 72.21.24.149
: 1748846565:0;cd
: 1748846568:0;code ,
: 1748846585:0;code .
