# SureCLIQ - Digital Marketing Services

A comprehensive digital marketing platform offering SMS Marketing, WhatsApp Marketing, RCS Messaging, and Digital Marketing services.

## Features

- **SMS Marketing**: Engage customers with personalized SMS campaigns
- **WhatsApp Marketing**: Reach customers on their preferred messaging platform
- **RCS Messaging**: Rich communication services for enhanced messaging
- **Digital Marketing**: Complete digital marketing solutions including SEO, PPC, SMO, and more

## New Digital Marketing Page

The latest addition includes a comprehensive Digital Marketing services page featuring:

- SEO Services
- PPC Advertising
- Social Media Marketing (SMO)
- Email Marketing
- Online Reputation Management (ORM)
- Google Analytics integration

## Running with Docker

### Prerequisites

- Docker
- Docker Compose

### Quick Start

1. Clone the repository
2. Navigate to the project directory
3. Run the application:

```bash
docker-compose up -d
```

4. Access the application at `http://localhost:8080`

### Manual Docker Build

```bash
# Build the Docker image
docker build -t surecliq-app .

# Run the container
docker run -d -p 8080:80 --name surecliq surecliq-app
```

## Project Structure

- `index.php` - Homepage
- `digital-marketing.php` - New Digital Marketing services page
- `sms-marketing.php` - SMS Marketing page
- `whatsapp-marketing.php` - WhatsApp Marketing page
- `rcs.php` - RCS Messaging page
- `header.php` - Common header with navigation
- `footer.php` - Common footer
- `solutions.php` - Solutions overview page
- `uploads/` - Images and assets
- `css/` - Stylesheets
- `js/` - JavaScript files

## Development

The application is built with:

- PHP 8.1
- Apache Web Server
- Bootstrap CSS Framework
- jQuery
- Font Awesome Icons
- Bootstrap Icons

## License

All rights reserved - SureCLIQ
