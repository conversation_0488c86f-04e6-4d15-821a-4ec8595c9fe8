<?php $MENU='whatsapp'; require_once('header.php'); ?>

<style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');
      
      p{
        font-family: system-ui;
        text-align: justify;
        font-size:18px !important;

      }
        /* Banner styles */
        .banner {
            /* background-image: url(''); */
          display: flex;
          /* align-items: center; */
          color: black;
          margin-top: 120px;
          background-color: #ffde59;
        }
        .banner-text {
          /* max-width: 600px; */
          /* padding: 25px 30px; */
          border-radius: 12px;
            position: absolute;
            margin: 0px 60px;
            max-width: 70%;
    padding: 25px 58px;
    margin-top: 146px;
        }
        .banner-text h1 {
          margin: 0 0 15px;
          font-size: 80px;
          font-weight: 700;
          letter-spacing: 1.2px;
          color: black;

        }
        .banner-text p {
            font-size: 18px;
          font-weight: 400;
          line-height: 1.4;
          margin-left: 14px;
        }
      
        /* Container */
        .containerx {
          max-width: 85%;
          margin: 0 auto;
          padding: 60px 20px 100px;
        }
      
        /* Each section */
        section {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 90px;
          gap: 40px;
        }
      
        /* Alternate section direction */
        section.even {
          flex-direction: row-reverse;
        }
      
        .content {
          flex: 1 1 520px;
        }
        .content h2 {
          font-weight: 700;
          font-size: 2.8rem;
          margin-bottom: 22px;
          color: #fed557;
        }
        .content p {
          font-size: 18px;
          margin-bottom: 20px;
          color: #444;
        }
        .content ul {
          list-style-type: disc;
          padding-left: 22px;
          color: #444;
          font-size: 18px;
        }
        .content ul li {
          margin-bottom: 14px;
        }
      
        /* Image container */
        .image-container {
          flex: 1 1 520px;
          text-align: center;
        }
        .image-container img {
          max-width: 100%;
          border-radius: 20px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
          transition: transform 0.3s ease;
          max-height: 420px;
          object-fit: cover;
        }
        .image-container img:hover {
          transform: scale(1.07);
        }
      
        /* Animation base and effect */
        .slide-in-left, .slide-in-right {
          opacity: 0;
          transform: translateX(0);
          transition: transform 0.9s ease, opacity 0.9s ease;
        }
        .slide-in-left {
          transform: translateX(-60px);
        }
        .slide-in-right {
          transform: translateX(60px);
        }
        .slide-in-left.active,
        .slide-in-right.active {
          opacity: 1;
          transform: translateX(0);
        }

        .banner img{
            width: 100%;
            height: 70%;
            margin: 0px auto !important;
        }
      
        /* Responsive for smaller devices */
        @media (max-width: 860px) {
          section {
            flex-direction: column !important;
            margin-bottom: 70px;
          }
          .content, .image-container {
            flex: 1 1 100%;
            padding: 10px 0;
          }
          .banner-text h1 {
            font-size: 55px;
            color: white;
          }
          .banner-text {
            padding: 0px ;
            color: white;

          }
          .banner-text p {
            font-size: 12px;
            color: white;

          }
          .banner{
            margin-top: 0px;
            color: white;

          }
          .banner img{
            width: 900px;
            height: 650px;
          }
        }
      
      
      </style>
         



    <!-- <div id="home" class="parallax first-section" data-stellar-background-ratio="0.4" style="background-image:url('uploads/parallax_12.jpg');"> -->
      <div id="home" class="parallax first-section" data-stellar-background-ratio="0.4" style="background-color:#fed557;">
        <div class="container">
            <div class="row">
                <div class="col-md-6 col-sm-12">
                    <div class="big-tagline">
                        <h2 style="color:black !important;">WhatsApp Marketing</h2>
                        <p style="color:black !important;     margin-top: -25px;" class="lead">Boost Your Business with Effective WhatsApp Marketing Strategies. Reach the right audience with
                            effective,<br> measurable WhatsApp campaigns that boost your conversions.</p>
                        <!-- <a href="https://my.surecliq.com/account/signup" class="btn btn-light btn-radius btn-brd ban-btn" style="border-radius: 50px;">Start Sending</a> -->
                        <a href="https://name.surecliq.com/console/#signup" class="btn btn-light btn-radius btn-brd ban-btn" style="border-radius: 50px;">Start Sending</a>
                    </div>
                </div>


                <div class="col-md-6 col-sm-12">
                  <img style="margin-top: 50px;" src="uploads/SureCLIQ14-short1.png" alt="" class="img-responsive">
              </div>

                <!-- <div class="app_iphone_02 wow slideInUp hidden-xs hidden-sm" data-wow-duration="1s"
                    data-wow-delay="0.5s">
                    <img src="uploads/rocket.png" alt="" class="img-responsive">
                </div> -->
            </div><!-- end row -->
        </div><!-- end container -->
    </div><!-- end section -->

    <svg id="clouds" class="hidden-xs" xmlns="http://www.w3.org/2000/svg" version="1.1" width="100%" height="100"
        viewBox="0 0 85 100" preserveAspectRatio="none">
        <path d="M-5 100 Q 0 20 5 100 Z
            M0 100 Q 5 0 10 100
            M5 100 Q 10 30 15 100
            M10 100 Q 15 10 20 100
            M15 100 Q 20 30 25 100
            M20 100 Q 25 -10 30 100
            M25 100 Q 30 10 35 100
            M30 100 Q 35 30 40 100
            M35 100 Q 40 10 45 100
            M40 100 Q 45 50 50 100
            M45 100 Q 50 20 55 100
            M50 100 Q 55 40 60 100
            M55 100 Q 60 60 65 100
            M60 100 Q 65 50 70 100
            M65 100 Q 70 20 75 100
            M70 100 Q 75 45 80 100
            M75 100 Q 80 30 85 100
            M80 100 Q 85 20 90 100
            M85 100 Q 90 50 95 100
            M90 100 Q 95 25 100 100
            M95 100 Q 100 15 105 100 Z">
        </path>
    </svg>
    
    <div class="containerx" role="main">

        <!-- Section 1: Introduction -->
        <section>
            <div class="content slide-in-left">
                <h2>What is WhatsApp Marketing?</h2>
                <p>WhatsApp marketing leverages the power of the world’s most popular messaging app to connect directly
                    with customers in a personal, instantaneous, and cost-effective way. With over 2 billion users
                    globally, WhatsApp has become a vital platform for businesses to promote products, provide customer
                    support, and build meaningful relationships.</p>
                <p>In this comprehensive guide, you’ll explore what WhatsApp marketing is, why it matters, how to use it
                    effectively, and some real-world examples to inspire your campaigns. Whether you are a small
                    business owner or a marketing professional, this guide will help you unlock the full potential of
                    WhatsApp for business growth.</p>
            </div>
            <div class="image-container slide-in-right">
                <img src="uploads/wt2.png"
                    alt="Person holding smartphone sending messages" />
            </div>
        </section>

        <!-- Section 2: Why Use SMS Marketing -->
        <section class="even">
            <div class="content slide-in-right">
                <h2>Why WhatsApp Marketing Matters?</h2>
                <p>Unlike traditional marketing channels, WhatsApp offers a unique combination of immediacy,
                    personalization, and high engagement rates. Here are key reasons why businesses are adopting
                    WhatsApp marketing:</p>
                <ul>
                    <li><strong>Direct and Personal Communication:</strong> Messages reach customers’ personal devices, creating a sense of intimacy and trust.</li>
                    <li><strong>High Open and Response Rates:</strong> Compared to emails or social media, WhatsApp messages typically get faster and more frequent responses.</li>
                    <li><strong>Cost-Effective: </strong> It is a low-cost channel to reach a large audience globally without expensive advertising budgets.</li>
                    <li><strong>Rich Media Support:</strong> Share images, videos, voice notes, and documents to create engaging content.</li>
                    <li><strong>Automation & Integration:</strong>  WhatsApp Business API allows automation, quick replies, and integration with CRM systems.</li>
                </ul>
                <p>By tapping into these advantages, businesses can foster brand loyalty, increase sales, and provide superior customer service.</p>
            </div>
            <div class="image-container slide-in-left">
                <img src="uploads/wt1.png"
                    alt="Person working on marketing campaign laptop" />
            </div>
        </section>

        <!-- Section 3: Features of SMS Marketing -->
        <section>
            <div class="content slide-in-left">
                <h2>Key Features of WhatsApp for Marketing</h2>
                <p>Understanding WhatsApp's features will help you make the most out of your marketing efforts. Some important features include:</p>
                <ul>
                    <li><strong>WhatsApp Business App:</strong> A free app for small businesses to create business profiles and communicate with customers.</li>
                    <li><strong>WhatsApp Business API:</strong> Designed for medium to large businesses to automate messaging, send bulk notifications, and integrate with other platforms.</li>
                    <li><strong>Broadcast Lists:</strong> Send messages to multiple contacts at once without showing recipients who else received the message.</li>
                    <li><strong>Labels and Quick Replies:</strong> Organize chats and save templated responses to efficiently manage communication.</li>
                    <li><strong>Rich Messaging:</strong> Use text, images, links, GIFs, audio, and video to create interactive and attractive messages.</li>
                    <li><strong>End-to-End Encryption:</strong> Ensures secure communication between you and your customers.</li>
                  </ul>
            </div>
            <div class="image-container slide-in-right">
                <img src="uploads/wt3.png"
                    alt="Marketing dashboard showing SMS campaign statistics" />
            </div>
        </section>




        <div class="row">
            <section style="display: block !important; padding: 20px !important;">
                <h2 style="    font-weight: 700;
                font-size: 2.8rem;
                margin-bottom: 22px;
                color: #fed557;">Effective WhatsApp Marketing Strategies</h2>
                <p>Now that you know why WhatsApp marketing is essential and what features to leverage, let’s explore some proven strategies that can help you achieve your marketing goals.</p>
                <h3>1. Personalized Customer Engagement</h3>
                <p>Personalization is key to building trust and loyalty. Use customer data to tailor your WhatsApp messages. Greet customers by their names, remember their preferences, and offer customized deals. For example, send a birthday discount or product recommendations based on their past purchases.</p>
                <h3>2. Broadcast Exclusive Offers and Updates</h3>
                <p>Create segmented broadcast lists to send exclusive promotions, flash sales, new product announcements, and company news. This method allows you to reach interested customers without spamming unrelated contacts.</p>
                <h3>3. Provide Real-Time Customer Support</h3>
                <p>With WhatsApp, customers can reach you instantly for questions or complaints. Use quick replies and automated messages to acknowledge queries and provide timely solutions. Offering such responsiveness boosts customer satisfaction and brand reputation.</p>
                <h3>4. Use Rich Media to Capture Attention</h3>
                <p>WhatsApp supports images, videos, GIFs, and voice notes, which helps convey your message in an engaging way. Share product demos, behind-the-scenes footage, or quick tutorials. For instance, a video showcasing a product’s features can increase users’ interest more effectively than text alone.</p>
                <h3>5. Leverage WhatsApp Groups and Communities</h3>
                <p>Create groups to build communities around your brand, share valuable content, and foster conversations. These groups can be used for customer feedback, product launches, or interest-based discussion, creating a loyal customer base with a sense of belonging.</p>
                <h3>6. Cart Recovery and Follow-Up Messages</h3>
                <p>For e-commerce businesses, sending reminders to customers who abandoned their shopping carts via WhatsApp can significantly improve conversions. Follow these reminders with personalized offers to motivate completion of purchase.</p>
                <h3>7. Conduct Polls and Surveys</h3>
                <p>Test different ideas and collect customer feedback through short polls and surveys shared on WhatsApp. This engagement helps you understand your audience better and tailor future campaigns accordingly.</p>
              </section>

<div style="display: block; text-align: center;">
    <!-- <a href="https://my.surecliq.com/account/signup" target="_blank" style="    color: white; -->
    <a href="https://name.surecliq.com/console/#signup" target="_blank" style="    color: white;
    background-color: #fed557;
    border-radius: 50px;
    padding: 20px;
    font-weight: bold;" rel="noopener noreferrer">Get Started with WhatsApp Business</a>
</div> 
        </div>


    </div>

    <div id="testimonials" class="section wb">
        <div class="container">
            <div class="section-title text-center">
                <h3>Happy Clients</h3>
                <p class="lead" style="text-align:center;">We thanks for all our awesome testimonials! There are hundreds of our happy customers!
                    <br>Let's see what others say about SureCLIQ!
                </p>
            </div><!-- end title -->

            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="testi-carousel owl-carousel owl-theme">
                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Boosted ROI!</h3>
                                <p class="lead">"SureCLIQ has revolutionized how we reach our audience. Their real-time
                                    tracking and multi-channel campaigns have boosted our ROI by over 15% in just
                                    months."</p>
                            </div>
                            <div class="testi-meta">
                                <h4>Alex Morgan<small>- Marketing Director</small></h4>
                            </div>
                            <!-- end testi-meta -->
                        </div>
                        <!-- end testimonial -->

                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Cost Efficiency!</h3>
                                <p class="lead">"The ability to target users across continents with localized precision
                                    is a game changer. Our campaign costs have significantly dropped while conversions
                                    soared."</p>
                            </div>
                            <div class="testi-meta">
                                <h4>Jacques Philips <small>- Designer</small></h4>
                            </div>
                            <!-- end testi-meta -->
                        </div>
                        <!-- end testimonial -->

                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Intuitive Dashboard!</h3>
                                <p class="lead">"The dashboard is incredibly intuitive, saving our team hours every
                                    week. The insights we get help us make smarter decisions faster." </p>
                            </div>
                            <div class="testi-meta">
                                <h4>Michael Chen <small>-CMO</small></h4>
                            </div>
                            <!-- end testi-meta -->
                        </div>
                        <!-- end testimonial -->
                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Scalable Platform!</h3>
                                <p class="lead">"From small batches to millions of users, SureCLIQ’s platform scales
                                    effortlessly. Their support team is responsive and truly understands our industry."
                                </p>
                            </div>
                            <div class="testi-meta">
                                <h4>Lara Singh <small>- Digital Entertainment</small></h4>
                            </div>
                            <!-- end testi-meta -->
                        </div>
                        <!-- end testimonial -->

                    </div><!-- end carousel -->
                </div><!-- end col -->
            </div><!-- end row -->
        </div><!-- end container -->
    </div><!-- end section -->


<?php require_once('footer.php'); ?>


<script>
    const slideElements = document.querySelectorAll('.slide-in-left, .slide-in-right');

    const observerOptions = {
        threshold: 0.15
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('active');
            }
        });
    }, observerOptions);

    slideElements.forEach(el => {
        observer.observe(el);
    });
</script>


<script>
    // Get the image element
const image = document.getElementById('image');

// Function to change the image on mobile screens
function changeImage() {
  // Check if the screen width is less than or equal to 768px
  if (window.innerWidth <= 768) {
    // Add the mobile-image class to the image element
    image.src = 'uploads/banner3s.jpg';
  } else {
    // Remove the mobile-image class from the image element
    image.src = 'uploads/SureCLIQ14.jpg';
  }
}

// Call the function when the page loads
window.onload = changeImage;

// Call the function when the window is resized
window.addEventListener('resize', changeImage);

</script>