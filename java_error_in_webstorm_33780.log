#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGABRT (0x6) at pc=0x00007ff81c276c52, pid=33780, tid=259
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.5+8-631.30-jcef (21.0.5+8) (build 21.0.5+8-b631.30)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.5+8-631.30-jcef (21.0.5+8-b631.30, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, bsd-amd64)
# Problematic frame:
# C  [libsystem_kernel.dylib+0x7c52]  __pthread_kill+0xa
#
# No core dump will be written. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://youtrack.jetbrains.com/issues/JBR
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  S U M M A R Y ------------

Command Line: abort vfprintf -XX:ErrorFile=/Users/<USER>/java_error_in_webstorm_%p.log -XX:HeapDumpPath=/Users/<USER>/java_error_in_webstorm.hprof -Xms128m -Xmx2048m -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:-OmitStackTraceInFastThrow -XX:CICompilerCount=2 -XX:+IgnoreUnrecognizedVMOptions -ea -Dsun.io.useCanonCaches=false -Dsun.java2d.metal=true -Djbr.catch.SIGABRT=true -Djdk.http.auth.tunneling.disabledSchemes="" -Djdk.attach.allowAttachSelf=true -Djdk.module.illegalAccess.silent=true -Djdk.nio.maxCachedBufferSize=2097152 -Djava.util.zip.use.nio.for.zip.file.access=true -Dkotlinx.coroutines.debug=off -XX:+UnlockDiagnosticVMOptions -XX:TieredOldPercentage=100000 -Denable.non.commercial.ai.license=true -Dapple.awt.application.appearance=system -Djb.vmOptionsFile=/Applications/WebStorm.app/Contents/Resources/../bin/webstorm.vmoptions -Djava.system.class.loader=com.intellij.util.lang.PathClassLoader -Didea.vendor.name=JetBrains -Didea.paths.selector=WebStorm2024.3 -Djna.boot.library.path=/Applications/WebStorm.app/Contents/lib/jna/amd64 -Dpty4j.preferred.native.folder=/Applications/WebStorm.app/Contents/lib/pty4j -Djna.nosys=true -Djna.noclasspath=true -Dintellij.platform.runtime.repository.path=/Applications/WebStorm.app/Contents/modules/module-descriptors.jar -Didea.platform.prefix=WebStorm -Dsplash=true -Daether.connector.resumeDownloads=false -Dcompose.swing.render.on.graphics=true --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.ref=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.text=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED --add-opens=java.base/jdk.internal.vm=ALL-UNNAMED --add-opens=java.base/sun.net.dns=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED --add-opens=java.base/sun.security.ssl=ALL-UNNAMED --add-opens=java.base/sun.security.util=ALL-UNNAMED --add-opens=java.desktop/com.apple.eawt=ALL-UNNAMED --add-opens=java.desktop/com.apple.eawt.event=ALL-UNNAMED --add-opens=java.desktop/com.apple.laf=ALL-UNNAMED --add-opens=java.desktop/com.sun.java.swing=ALL-UNNAMED --add-opens=java.desktop/java.awt=ALL-UNNAMED --add-opens=java.desktop/java.awt.dnd.peer=ALL-UNNAMED --add-opens=java.desktop/java.awt.event=ALL-UNNAMED --add-opens=java.desktop/java.awt.font=ALL-UNNAMED --add-opens=java.desktop/java.awt.image=ALL-UNNAMED --add-opens=java.desktop/java.awt.peer=ALL-UNNAMED --add-opens=java.desktop/javax.swing=ALL-UNNAMED --add-opens=java.desktop/javax.swing.plaf.basic=ALL-UNNAMED --add-opens=java.desktop/javax.swing.text=ALL-UNNAMED --add-opens=java.desktop/javax.swing.text.html=ALL-UNNAMED --add-opens=java.desktop/sun.awt=ALL-UNNAMED --add-opens=java.desktop/sun.awt.datatransfer=ALL-UNNAMED --add-opens=java.desktop/sun.awt.image=ALL-UNNAMED --add-opens=java.desktop/sun.font=ALL-UNNAMED --add-opens=java.desktop/sun.java2d=ALL-UNNAMED --add-opens=java.desktop/sun.lwawt=ALL-UNNAMED --add-opens=java.desktop/sun.lwawt.macosx=ALL-UNNAMED --add-opens=java.desktop/sun.swing=ALL-UNNAMED --add-opens=java.management/sun.management=ALL-UNNAMED --add-opens=jdk.attach/sun.tools.attach=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-opens=jdk.internal.jvmstat/sun.jvmstat.monitor=ALL-UNNAMED --add-opens=jdk.jdi/com.sun.tools.jdi=ALL-UNNAMED -Dide.native.launcher=true com.intellij.idea.Main

Host: "MacBookPro15,4" x86_64 1400 MHz, 8 cores, 8G, Darwin 24.3.0, macOS 15.3.1 (24D70)
Time: Wed Mar  5 22:17:41 2025 +0545 elapsed time: 52.594038 seconds (0d 0h 0m 52s)

---------------  T H R E A D  ---------------

Current thread (0x00007f80d483b600):  JavaThread "AppKit Thread" daemon [_thread_in_native, id=259, stack(0x00007ff7bad65000,0x00007ff7bb565000) (8192K)]

Stack: [0x00007ff7bad65000,0x00007ff7bb565000],  sp=0x00007ff7bb561738,  free space=8177k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [libsystem_kernel.dylib+0x7c52]  __pthread_kill+0xa
C  [libsystem_c.dylib+0x80b19]  abort+0x7e
C  [libc++abi.dylib+0x11163]  __cxxabiv1::__aligned_malloc_with_fallback(unsigned long)+0x0
C  [libc++abi.dylib+0x20ce]  demangling_terminate_handler()+0x10a
C  [libobjc.A.dylib+0x1cd61]  _objc_terminate()+0x60
C  [libc++abi.dylib+0x1053b]  std::__terminate(void (*)())+0x6
C  [libc++abi.dylib+0x104f6]  std::terminate()+0x36
C  [libdispatch.dylib+0x37f6]  _dispatch_client_callout+0x1c
C  [libdispatch.dylib+0xfe43]  _dispatch_main_queue_drain+0x3e7
C  [libdispatch.dylib+0xfa4e]  _dispatch_main_queue_callback_4CF+0x1f
C  [CoreFoundation+0xb94e8]  __CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__+0x9
C  [CoreFoundation+0x7ac6d]  __CFRunLoopRun+0x9cf
C  [CoreFoundation+0x79c6e]  CFRunLoopRunSpecific+0x226
C  [HIToolbox+0xef413]  RunCurrentEventLoopInMode+0x124
C  [HIToolbox+0xf4d63]  ReceiveNextEventCommon+0x1f5
C  [HIToolbox+0xf5029]  _BlockUntilNextEventMatchingListInModeWithFilter+0x42
C  [AppKit+0x40b33]  _DPSNextEvent+0x386
C  [AppKit+0xa958b8]  -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]+0x50a
C  [libosxapp.dylib+0x311a]  -[NSApplicationAWT nextEventMatchingMask:untilDate:inMode:dequeue:]+0x7a
C  [AppKit+0x31aa9]  -[NSApplication run]+0x262
C  [libosxapp.dylib+0x2ee5]  +[NSApplicationAWT runAWTLoopWithApp:]+0xa5
C  [libawt_lwawt.dylib+0x94850]  +[AWTStarter starter:headless:]+0x1f0
C  [libosxapp.dylib+0x4edf]  +[ThreadUtilities invokeBlockCopy:]+0xf
C  [Foundation+0x7c1a9]  __NSThreadPerformPerform+0xb2
C  [CoreFoundation+0x7bca9]  __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__+0x11
C  [CoreFoundation+0x7bc4b]  __CFRunLoopDoSource0+0x9d
C  [CoreFoundation+0x7ba1e]  __CFRunLoopDoSources0+0xcb
C  [CoreFoundation+0x7a65e]  __CFRunLoopRun+0x3c0
C  [CoreFoundation+0x79c6e]  CFRunLoopRunSpecific+0x226
C  [webstorm+0x6b8e2]  xplat_launcher::main_lib::h96bf2f3d0ce769d3+0x2fae
C  [webstorm+0x2076]  std::sys_common::backtrace::__rust_begin_short_backtrace::h01a6c7c3091790cf+0x6
C  [webstorm+0x236a]  main+0x2da
C  [dyld+0x62cd]  start+0x70d

siginfo: si_signo: 6 (SIGABRT), si_code: 0 (unknown)

Registers:
RAX=0x0000000000000000, RBX=0x0000000000000006, RCX=0x00007ff7bb561738, RDX=0x0000000000000000
RSP=0x00007ff7bb561738, RBP=0x00007ff7bb561760, RSI=0x0000000000000006, RDI=0x0000000000000103
R8 =0x00007ff7bb561600, R9 =0xffffffffffffffff, R10=0x0000000000000000, R11=0x0000000000000246
R12=0x00007ff85f1efa00, R13=0x0000003000000008, R14=0x0000000000000103, R15=0x0000000000000016
RIP=0x00007ff81c276c52, EFLAGS=0x0000000000000246, ERR=0x0000000002000148
  TRAPNO=0x0000000000000085


Register to memory mapping:

RAX=0x0 is null
RBX=0x0000000000000006 is an unknown value
RCX=0x00007ff7bb561738 is pointing into the stack for thread: 0x00007f80d483b600
RDX=0x0 is null
RSP=0x00007ff7bb561738 is pointing into the stack for thread: 0x00007f80d483b600
RBP=0x00007ff7bb561760 is pointing into the stack for thread: 0x00007f80d483b600
RSI=0x0000000000000006 is an unknown value
RDI=0x0000000000000103 is an unknown value
R8 =0x00007ff7bb561600 is pointing into the stack for thread: 0x00007f80d483b600
R9 =0xffffffffffffffff: __rust_no_alloc_shim_is_unstable+0xfb5d8947 in /Applications/WebStorm.app/Contents/MacOS/webstorm at 0x000000010499b000
R10=0x0 is null
R11=0x0000000000000246 is an unknown value
R12=0x00007ff85f1efa00: __stderrp+0 in /usr/lib/system/libsystem_c.dylib at 0x00007ff81c151000
R13=0x0000003000000008 is an unknown value
R14=0x0000000000000103 is an unknown value
R15=0x0000000000000016 is an unknown value

Top of Stack: (sp=0x00007ff7bb561738)
0x00007ff7bb561738:   00007ff81c2b0f85 00007ff85f005b00
0x00007ff7bb561748:   00007ff85f1efa00 00007ff7bb561778
0x00007ff7bb561758:   0000000000000003 00007ff7bb5617a0
0x00007ff7bb561768:   00007ff81c1d1b19 7485c239859600eb
0x00007ff7bb561778:   00007ff8fffff9df ffffffff5f1efa00
0x00007ff7bb561788:   00007ff81c26bb9f 00007ff7bb561868
0x00007ff7bb561798:   00007ff7bb5617b0 00007ff7bb5618c0
0x00007ff7bb5617a8:   00007ff81c268163 00007ff85c55c7f0
0x00007ff7bb5617b8:   00007ff81c26bbca 00007ff81c75728e
0x00007ff7bb5617c8:   00007ff81bf1165a 0000000000000078
0x00007ff7bb5617d8:   ffffffffffffffc9 7485c239859600eb
0x00007ff7bb5617e8:   00007ff85f007448 00000000c2000000
0x00007ff7bb5617f8:   00007ff822d54ba5 00007ff85c983938
0x00007ff7bb561808:   00006000033b1a70 00007ff7bb561840
0x00007ff7bb561818:   00007ff8202b653b 00007ff7bb560000
0x00007ff7bb561828:   00007ff81c4083af 000000000000008a
0x00007ff7bb561838:   00006000033b1a70 00007ff7bb561900
0x00007ff7bb561848:   00007ff81c4a3c6b 7485c239859600eb
0x00007ff7bb561858:   00007ff83aecf665 00006000033b19b0
0x00007ff7bb561868:   0000600004f85e80 0000003000000018
0x00007ff7bb561878:   00007ff7bb5618d0 00007ff7bb5617b0
0x00007ff7bb561888:   00007ff81c259178 7485c239859600eb
0x00007ff7bb561898:   00007ff81c75728e 00007ff81c259158
0x00007ff7bb5618a8:   0000600000218d00 00007ff7bb5618d8
0x00007ff7bb5618b8:   00006000096618e8 00007ff7bb561900
0x00007ff7bb5618c8:   00007ff81c2590ce 7485c239859600eb
0x00007ff7bb5618d8:   00006000096618e0 0000000000000002
0x00007ff7bb5618e8:   0000000000000000 0000000000000114
0x00007ff7bb5618f8:   0000000000000000 00007ff7bb561920
0x00007ff7bb561908:   00007ff81bef5d61 00434c4e47432b01
0x00007ff7bb561918:   0000600009661860 00007ff7bb561930
0x00007ff7bb561928:   00007ff81c26753b 00007ff7bb561950 

Instructions: (pc=0x00007ff81c276c52)
0x00007ff81c276b52:   fe ff ff eb 0f 83 bd 40 ff ff ff 00 74 27 41 be
0x00007ff81c276b62:   d4 fe ff ff 48 8d bd 38 ff ff ff e8 1d a0 ff ff
0x00007ff81c276b72:   44 89 f0 48 81 c4 a8 00 00 00 5b 41 5c 41 5d 41
0x00007ff81c276b82:   5e 41 5f 5d c3 44 8b b5 58 ff ff ff 45 85 f6 75
0x00007ff81c276b92:   d3 44 8b a5 70 ff ff ff 49 83 fc 13 77 c0 8d 48
0x00007ff81c276ba2:   c4 c1 e9 02 44 39 e1 72 b5 4c 89 e2 48 c1 e2 02
0x00007ff81c276bb2:   8d 4a 3c 41 be d4 fe ff ff 39 c8 75 a7 48 8d b5
0x00007ff81c276bc2:   74 ff ff ff 48 8b 46 e8 49 89 45 00 48 8b 46 f0
0x00007ff81c276bd2:   48 8b 4d c8 48 89 01 8b 46 f8 41 89 07 8b 03 41
0x00007ff81c276be2:   39 c4 76 1e 48 c1 e0 02 48 8b 7d d0 48 89 c2 e8
0x00007ff81c276bf2:   77 95 ff ff 44 89 23 41 be cd fe ff ff e9 6e ff
0x00007ff81c276c02:   ff ff 48 8b 7d d0 e8 60 95 ff ff 44 89 23 45 31
0x00007ff81c276c12:   f6 e9 5a ff ff ff b8 64 00 00 02 49 89 ca 0f 05
0x00007ff81c276c22:   73 08 48 89 c7 e9 36 9a ff ff c3 00 00 00 b8 be
0x00007ff81c276c32:   01 00 02 49 89 ca 0f 05 73 08 48 89 c7 e9 1e 9a
0x00007ff81c276c42:   ff ff c3 00 00 00 b8 48 01 00 02 49 89 ca 0f 05
0x00007ff81c276c52:   73 08 48 89 c7 e9 06 9a ff ff c3 00 00 00 b8 53
0x00007ff81c276c62:   00 00 02 49 89 ca 0f 05 73 08 48 89 c7 e9 ee 99
0x00007ff81c276c72:   ff ff c3 00 00 00 b8 83 01 00 02 49 89 ca 0f 05
0x00007ff81c276c82:   73 08 48 89 c7 e9 d6 99 ff ff c3 00 00 00 45 85
0x00007ff81c276c92:   c0 74 23 65 48 8b 04 25 08 00 00 00 48 85 c0 48
0x00007ff81c276ca2:   8d 0d 18 96 d9 42 48 0f 45 c8 c7 01 16 00 00 00
0x00007ff81c276cb2:   b8 ff ff ff ff c3 41 b8 40 00 00 00 e9 ad bd ff
0x00007ff81c276cc2:   ff 00 b8 6a 00 00 02 49 89 ca 0f 05 73 08 48 89
0x00007ff81c276cd2:   c7 e9 8a 99 ff ff c3 00 00 00 b8 1e 00 00 02 49
0x00007ff81c276ce2:   89 ca 0f 05 73 08 48 89 c7 e9 8b a6 ff ff c3 00
0x00007ff81c276cf2:   00 00 b8 e6 00 00 02 49 89 ca 0f 05 73 08 48 89
0x00007ff81c276d02:   c7 e9 73 a6 ff ff c3 00 00 b8 16 00 00 00 48 85
0x00007ff81c276d12:   ff 74 1e 48 8b 3f 48 85 ff 74 16 81 ce 00 80 00
0x00007ff81c276d22:   00 66 89 77 40 89 57 44 89 4f 48 44 89 47 4c 31
0x00007ff81c276d32:   c0 c3 b8 51 00 00 02 49 89 ca 0f 05 73 08 48 89
0x00007ff81c276d42:   c7 e9 1a 99 ff ff c3 00 48 83 3d 0e 0c f8 42 00 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x00007ff81c2b0f85: pthread_kill+0x106 in /usr/lib/system/libsystem_pthread.dylib at 0x00007ff81c2ab000
stack at sp + 1 slots: 0x00007ff85f005b00 points into unknown readable memory: 0x8415448717b9f510 | 10 f5 b9 17 87 44 15 84
stack at sp + 2 slots: 0x00007ff85f1efa00: __stderrp+0 in /usr/lib/system/libsystem_c.dylib at 0x00007ff81c151000
stack at sp + 3 slots: 0x00007ff7bb561778 is pointing into the stack for thread: 0x00007f80d483b600
stack at sp + 4 slots: 0x0000000000000003 is an unknown value
stack at sp + 5 slots: 0x00007ff7bb5617a0 is pointing into the stack for thread: 0x00007f80d483b600
stack at sp + 6 slots: 0x00007ff81c1d1b19: abort+0x7e in /usr/lib/system/libsystem_c.dylib at 0x00007ff81c151000
stack at sp + 7 slots: 0x7485c239859600eb is an unknown value


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00006000027816e0, length=136, elements={
0x00007f80d400b200, 0x00007f80d383b600, 0x00007f80d282de00, 0x00007f80d600ba00,
0x00007f80d282e600, 0x00007f80d3839a00, 0x00007f80d4066200, 0x00007f80d4066a00,
0x00007f80d300a000, 0x00007f80d482ca00, 0x00007f80d3862800, 0x00007f80d387e800,
0x00007f80d482f000, 0x00007f80d4087200, 0x00007f80d603c800, 0x00007f80d4089c00,
0x00007f80d603d000, 0x00007f80d2871800, 0x00007f80d3023600, 0x00007f80d3091a00,
0x00007f80d3092e00, 0x00007f80d3881600, 0x00007f80d700b800, 0x00007f80d40b0200,
0x00007f80d183c200, 0x00007f80d483b600, 0x00007f80d4125600, 0x00007f80d40bbc00,
0x00007f80d48a6000, 0x00007f80d28e4e00, 0x00007f80d30fbc00, 0x00007f80d293d800,
0x00007f80d39a1e00, 0x00007f80d311ec00, 0x00007f80d39b4600, 0x00007f80d419f400,
0x00007f80d7116800, 0x00007f80d7132a00, 0x00007f80d3880800, 0x00007f80d3889600,
0x00007f80d612fe00, 0x00007f80d614b400, 0x00007f80d4941200, 0x00007f80d423c000,
0x00007f80d603e400, 0x00007f80d7410a00, 0x00007f80d2bb7a00, 0x00007f80d41d3e00,
0x00007f80d6148200, 0x00007f80d1a7f200, 0x00007f80d2c6ce00, 0x00007f80d7428c00,
0x00007f80d7429400, 0x00007f80d3365400, 0x00007f80d335e400, 0x00007f80d2937600,
0x00007f80d3c28400, 0x00007f80d4942400, 0x00007f80d6319c00, 0x00007f80d1be2600,
0x00007f80d2b80600, 0x00007f80d61ab600, 0x00007f80d30d3e00, 0x00007f80d61abe00,
0x00007f80d4bf0200, 0x00007f80d30d6400, 0x00007f80d2ce4a00, 0x00007f80d71b5800,
0x00007f80d71d3600, 0x00007f80d6316c00, 0x00007f80d335a400, 0x00007f80d71d3e00,
0x00007f80d39e1a00, 0x00007f80d42b1800, 0x00007f80d626f600, 0x00007f80d626fe00,
0x00007f80d61ebc00, 0x00007f80d1cbd200, 0x00007f80d31ecc00, 0x00007f80d3284000,
0x00007f80d1ca9c00, 0x00007f80d6362a00, 0x00007f80d4b61c00, 0x00007f80d7408e00,
0x00007f80d18a9000, 0x00007f80d3282200, 0x00007f80d749cc00, 0x00007f80d2c6ba00,
0x00007f80d1ceb800, 0x00007f80d1cec000, 0x00007f80d6363a00, 0x00007f80d1ced000,
0x00007f80d3840400, 0x00007f80d31f2800, 0x00007f80d71cfc00, 0x00007f80d7430800,
0x00007f80d7431000, 0x00007f80d18e7c00, 0x00007f80d31f3800, 0x00007f80d30dee00,
0x00007f80d72bf600, 0x00007f80d18ea400, 0x00007f80d30e0a00, 0x00007f80d32a5600,
0x00007f80d18eb400, 0x00007f80d2993600, 0x00007f80d6054000, 0x00007f80d4a64a00,
0x00007f80d3ae0600, 0x00007f80d748a000, 0x00007f80d3ae0e00, 0x00007f80d3bbfa00,
0x00007f80d1cf6a00, 0x00007f80d32f3c00, 0x00007f80d49faa00, 0x00007f80d49fb200,
0x00007f80d6119400, 0x00007f80d748b000, 0x00007f80d6119c00, 0x00007f80d1c9b000,
0x00007f80d2b3ea00, 0x00007f80d2908200, 0x00007f80d1cf6000, 0x00007f80d4bb4c00,
0x00007f80d4454600, 0x00007f80d45c2200, 0x00007f80d2bfe600, 0x00007f80d1906800,
0x00007f80d4bee800, 0x00007f80d61a9400, 0x00007f80d3c9ee00, 0x00007f80d6116a00,
0x00007f80d7407600, 0x00007f80d18e9800, 0x00007f80d38db600, 0x00007f80d4093000
}

Java Threads: ( => current thread )
  0x00007f80d400b200 JavaThread "main"                              [_thread_blocked, id=7427, stack(0x0000700009f2e000,0x000070000a12e000) (2048K)]
  0x00007f80d383b600 JavaThread "Reference Handler"          daemon [_thread_blocked, id=30211, stack(0x000070000a94c000,0x000070000aa4c000) (1024K)]
  0x00007f80d282de00 JavaThread "Finalizer"                  daemon [_thread_blocked, id=29955, stack(0x000070000aa4f000,0x000070000ab4f000) (1024K)]
  0x00007f80d600ba00 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=23811, stack(0x000070000ab52000,0x000070000ac52000) (1024K)]
  0x00007f80d282e600 JavaThread "Service Thread"             daemon [_thread_blocked, id=29187, stack(0x000070000ac55000,0x000070000ad55000) (1024K)]
  0x00007f80d3839a00 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=24067, stack(0x000070000ad58000,0x000070000ae58000) (1024K)]
  0x00007f80d4066200 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=28419, stack(0x000070000ae5b000,0x000070000af5b000) (1024K)]
  0x00007f80d4066a00 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=28163, stack(0x000070000af5e000,0x000070000b05e000) (1024K)]
  0x00007f80d300a000 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=24835, stack(0x000070000b061000,0x000070000b161000) (1024K)]
  0x00007f80d482ca00 JavaThread "Notification Thread"        daemon [_thread_blocked, id=25347, stack(0x000070000b164000,0x000070000b264000) (1024K)]
  0x00007f80d3862800 JavaThread "DefaultDispatcher-worker-1" daemon [_thread_blocked, id=25859, stack(0x000070000b267000,0x000070000b367000) (1024K)]
  0x00007f80d387e800 JavaThread "DefaultDispatcher-worker-2" daemon [_thread_blocked, id=26371, stack(0x000070000b36a000,0x000070000b46a000) (1024K)]
  0x00007f80d482f000 JavaThread "DefaultDispatcher-worker-3" daemon [_thread_blocked, id=32771, stack(0x000070000b46d000,0x000070000b56d000) (1024K)]
  0x00007f80d4087200 JavaThread "DefaultDispatcher-worker-4" daemon [_thread_blocked, id=43011, stack(0x000070000b570000,0x000070000b670000) (1024K)]
  0x00007f80d603c800 JavaThread "DefaultDispatcher-worker-5" daemon [_thread_blocked, id=42499, stack(0x000070000b673000,0x000070000b773000) (1024K)]
  0x00007f80d4089c00 JavaThread "DefaultDispatcher-worker-6" daemon [_thread_blocked, id=33027, stack(0x000070000b776000,0x000070000b876000) (1024K)]
  0x00007f80d603d000 JavaThread "DefaultDispatcher-worker-7" daemon [_thread_blocked, id=41987, stack(0x000070000b879000,0x000070000b979000) (1024K)]
  0x00007f80d2871800 JavaThread "DefaultDispatcher-worker-8" daemon [_thread_blocked, id=41731, stack(0x000070000b97c000,0x000070000ba7c000) (1024K)]
  0x00007f80d3023600 JavaThread "DefaultDispatcher-worker-9" daemon [_thread_blocked, id=33795, stack(0x000070000ba7f000,0x000070000bb7f000) (1024K)]
  0x00007f80d3091a00 JavaThread "DefaultDispatcher-worker-10" daemon [_thread_blocked, id=34307, stack(0x000070000bb82000,0x000070000bc82000) (1024K)]
  0x00007f80d3092e00 JavaThread "DefaultDispatcher-worker-11" daemon [_thread_blocked, id=40963, stack(0x000070000bc85000,0x000070000bd85000) (1024K)]
  0x00007f80d3881600 JavaThread "DefaultDispatcher-worker-12" daemon [_thread_blocked, id=34563, stack(0x000070000bd88000,0x000070000be88000) (1024K)]
  0x00007f80d700b800 JavaThread "External Command Listener"  daemon [_thread_in_native, id=40451, stack(0x000070000be8b000,0x000070000bf8b000) (1024K)]
  0x00007f80d40b0200 JavaThread "Timer-0"                    daemon [_thread_blocked, id=40195, stack(0x000070000bf8e000,0x000070000c08e000) (1024K)]
  0x00007f80d183c200 JavaThread "kotlinx.coroutines.DefaultExecutor" daemon [_thread_blocked, id=39175, stack(0x000070000c39a000,0x000070000c49a000) (1024K)]
=>0x00007f80d483b600 JavaThread "AppKit Thread"              daemon [_thread_in_native, id=259, stack(0x00007ff7bad65000,0x00007ff7bb565000) (8192K)]
  0x00007f80d4125600 JavaThread "DefaultDispatcher-worker-13" daemon [_thread_blocked, id=44803, stack(0x000070000c49d000,0x000070000c59d000) (1024K)]
  0x00007f80d40bbc00 JavaThread "DefaultDispatcher-worker-14" daemon [_thread_blocked, id=64003, stack(0x000070000c5a0000,0x000070000c6a0000) (1024K)]
  0x00007f80d48a6000 JavaThread "process reaper (pid 33787)" daemon [_thread_in_native, id=47875, stack(0x000070000c9ac000,0x000070000c9d0000) (144K)]
  0x00007f80d28e4e00 JavaThread "AWT-Shutdown"                      [_thread_blocked, id=52231, stack(0x000070000cad9000,0x000070000cbd9000) (1024K)]
  0x00007f80d30fbc00 JavaThread "Java2D Queue Flusher"       daemon [_thread_blocked, id=77067, stack(0x000070000cd62000,0x000070000ce62000) (1024K)]
  0x00007f80d293d800 JavaThread "Java2D Disposer"            daemon [_thread_blocked, id=91907, stack(0x000070000ce65000,0x000070000cf65000) (1024K)]
  0x00007f80d39a1e00 JavaThread "AWT-EventQueue-0"                  [_thread_blocked, id=127491, stack(0x000070000cf68000,0x000070000d068000) (1024K)]
  0x00007f80d311ec00 JavaThread "PeriodicMetricReader"       daemon [_thread_blocked, id=93447, stack(0x000070000d16e000,0x000070000d26e000) (1024K)]
  0x00007f80d39b4600 JavaThread "Coroutines Debugger Cleaner" daemon [_thread_blocked, id=92939, stack(0x000070000d271000,0x000070000d371000) (1024K)]
  0x00007f80d419f400 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=97795, stack(0x000070000d374000,0x000070000d474000) (1024K)]
  0x00007f80d7116800 JavaThread "AWTThreading pool-1-thread-1" daemon [_thread_blocked, id=98315, stack(0x000070000d477000,0x000070000d577000) (1024K)]
  0x00007f80d7132a00 JavaThread "Kernel event loop thread 2udije6nhd124r9dag9m" daemon [_thread_blocked, id=122127, stack(0x000070000d680000,0x000070000d780000) (1024K)]
  0x00007f80d3880800 JavaThread "Periodic tasks thread"      daemon [_thread_blocked, id=121367, stack(0x000070000d783000,0x000070000d883000) (1024K)]
  0x00007f80d3889600 JavaThread "ApplicationImpl pooled thread 1"        [_thread_blocked, id=120339, stack(0x000070000d886000,0x000070000d986000) (1024K)]
  0x00007f80d612fe00 JavaThread "JVMResponsivenessMonitor"   daemon [_thread_blocked, id=100355, stack(0x000070000d989000,0x000070000da89000) (1024K)]
  0x00007f80d614b400 JavaThread "fsnotifier"                        [_thread_blocked, id=101891, stack(0x000070000dc92000,0x000070000dd92000) (1024K)]
  0x00007f80d4941200 JavaThread "BaseDataReader: output stream of fsnotifier"        [_thread_in_native, id=118531, stack(0x000070000dd95000,0x000070000de95000) (1024K)]
  0x00007f80d423c000 JavaThread "BaseDataReader: error stream of fsnotifier"        [_thread_in_native, id=118275, stack(0x000070000de98000,0x000070000df98000) (1024K)]
  0x00007f80d603e400 JavaThread "Netty Builtin Server 1"     daemon [_thread_in_native, id=118035, stack(0x000070000df9b000,0x000070000e09b000) (1024K)]
  0x00007f80d7410a00 JavaThread "ApplicationImpl pooled thread 2"        [_thread_blocked, id=107539, stack(0x000070000e121000,0x000070000e221000) (1024K)]
  0x00007f80d2bb7a00 JavaThread "DefaultDispatcher-worker-15" daemon [_thread_blocked, id=173319, stack(0x000070000e224000,0x000070000e324000) (1024K)]
  0x00007f80d41d3e00 JavaThread "DefaultDispatcher-worker-16" daemon [_thread_blocked, id=110367, stack(0x000070000e327000,0x000070000e427000) (1024K)]
  0x00007f80d6148200 JavaThread "DefaultDispatcher-worker-17" daemon [_thread_blocked, id=134147, stack(0x000070000e52d000,0x000070000e62d000) (1024K)]
  0x00007f80d1a7f200 JavaThread "DefaultDispatcher-worker-18" daemon [_thread_blocked, id=134403, stack(0x000070000e630000,0x000070000e730000) (1024K)]
  0x00007f80d2c6ce00 JavaThread "DefaultDispatcher-worker-19" daemon [_thread_blocked, id=134915, stack(0x000070000e733000,0x000070000e833000) (1024K)]
  0x00007f80d7428c00 JavaThread "DefaultDispatcher-worker-20" daemon [_thread_blocked, id=171779, stack(0x000070000e836000,0x000070000e936000) (1024K)]
  0x00007f80d7429400 JavaThread "DefaultDispatcher-worker-21" daemon [_thread_blocked, id=135683, stack(0x000070000e939000,0x000070000ea39000) (1024K)]
  0x00007f80d3365400 JavaThread "DefaultDispatcher-worker-22" daemon [_thread_blocked, id=136195, stack(0x000070000ea3c000,0x000070000eb3c000) (1024K)]
  0x00007f80d335e400 JavaThread "DefaultDispatcher-worker-23" daemon [_thread_blocked, id=171267, stack(0x000070000eb3f000,0x000070000ec3f000) (1024K)]
  0x00007f80d2937600 JavaThread "I/O pool 4"                        [_thread_blocked, id=105235, stack(0x000070000ecc5000,0x000070000edc5000) (1024K)]
  0x00007f80d3c28400 JavaThread "ApplicationImpl pooled thread 3"        [_thread_blocked, id=113211, stack(0x000070000edc8000,0x000070000eec8000) (1024K)]
  0x00007f80d4942400 JavaThread "TimerQueue"                 daemon [_thread_blocked, id=173615, stack(0x000070000eecb000,0x000070000efcb000) (1024K)]
  0x00007f80d6319c00 JavaThread "I/O pool 5"                        [_thread_blocked, id=133903, stack(0x000070000efce000,0x000070000f0ce000) (1024K)]
  0x00007f80d1be2600 JavaThread "ApplicationImpl pooled thread 4"        [_thread_blocked, id=172679, stack(0x000070000f0d1000,0x000070000f1d1000) (1024K)]
  0x00007f80d2b80600 JavaThread "ApplicationImpl pooled thread 5"        [_thread_blocked, id=148491, stack(0x000070000f1d4000,0x000070000f2d4000) (1024K)]
  0x00007f80d61ab600 JavaThread "process reaper"             daemon [_thread_blocked, id=172947, stack(0x000070000ec42000,0x000070000ec66000) (144K)]
  0x00007f80d30d3e00 JavaThread "I/O pool 6"                        [_thread_blocked, id=169539, stack(0x000070000f2d7000,0x000070000f3d7000) (1024K)]
  0x00007f80d61abe00 JavaThread "I/O pool 7"                        [_thread_blocked, id=149011, stack(0x000070000f45d000,0x000070000f55d000) (1024K)]
  0x00007f80d4bf0200 JavaThread "process reaper"             daemon [_thread_blocked, id=99099, stack(0x000070000ec69000,0x000070000ec8d000) (144K)]
  0x00007f80d30d6400 JavaThread "I/O pool 8"                        [_thread_blocked, id=109691, stack(0x000070000f560000,0x000070000f660000) (1024K)]
  0x00007f80d2ce4a00 JavaThread "I/O pool 9"                        [_thread_blocked, id=157187, stack(0x000070000f663000,0x000070000f763000) (1024K)]
  0x00007f80d71b5800 JavaThread "I/O pool 10"                       [_thread_blocked, id=156931, stack(0x000070000f766000,0x000070000f866000) (1024K)]
  0x00007f80d71d3600 JavaThread "I/O pool 11"                       [_thread_blocked, id=149515, stack(0x000070000f869000,0x000070000f969000) (1024K)]
  0x00007f80d6316c00 JavaThread "FileSystemWatcher"          daemon [_thread_blocked, id=142623, stack(0x000070000f96c000,0x000070000fa6c000) (1024K)]
  0x00007f80d335a400 JavaThread "process reaper"             daemon [_thread_blocked, id=163079, stack(0x000070000ec90000,0x000070000ecb4000) (144K)]
  0x00007f80d71d3e00 JavaThread "I/O pool 12"                       [_thread_blocked, id=142343, stack(0x000070000fa6f000,0x000070000fb6f000) (1024K)]
  0x00007f80d39e1a00 JavaThread "node"                              [_thread_blocked, id=138759, stack(0x000070000fb72000,0x000070000fc72000) (1024K)]
  0x00007f80d42b1800 JavaThread "DefaultDispatcher-worker-24" daemon [_thread_blocked, id=159019, stack(0x000070000fe7b000,0x000070000ff7b000) (1024K)]
  0x00007f80d626f600 JavaThread "DefaultDispatcher-worker-25" daemon [_thread_blocked, id=148067, stack(0x000070000ff7e000,0x000070001007e000) (1024K)]
  0x00007f80d626fe00 JavaThread "DefaultDispatcher-worker-26" daemon [_thread_blocked, id=164619, stack(0x0000700010081000,0x0000700010181000) (1024K)]
  0x00007f80d61ebc00 JavaThread "DefaultDispatcher-worker-28" daemon [_thread_blocked, id=131599, stack(0x0000700010184000,0x0000700010284000) (1024K)]
  0x00007f80d1cbd200 JavaThread "DefaultDispatcher-worker-27" daemon [_thread_blocked, id=162567, stack(0x0000700010287000,0x0000700010387000) (1024K)]
  0x00007f80d31ecc00 JavaThread "DefaultDispatcher-worker-29" daemon [_thread_blocked, id=168199, stack(0x000070001038a000,0x000070001048a000) (1024K)]
  0x00007f80d3284000 JavaThread "DefaultDispatcher-worker-32" daemon [_thread_blocked, id=139015, stack(0x000070001048d000,0x000070001058d000) (1024K)]
  0x00007f80d1ca9c00 JavaThread "DefaultDispatcher-worker-31" daemon [_thread_blocked, id=144647, stack(0x0000700010590000,0x0000700010690000) (1024K)]
  0x00007f80d6362a00 JavaThread "DefaultDispatcher-worker-30" daemon [_thread_blocked, id=140807, stack(0x0000700010693000,0x0000700010793000) (1024K)]
  0x00007f80d4b61c00 JavaThread "DefaultDispatcher-worker-33" daemon [_thread_blocked, id=131867, stack(0x0000700010796000,0x0000700010896000) (1024K)]
  0x00007f80d7408e00 JavaThread "DefaultDispatcher-worker-34" daemon [_thread_blocked, id=146695, stack(0x0000700010899000,0x0000700010999000) (1024K)]
  0x00007f80d18a9000 JavaThread "DefaultDispatcher-worker-35" daemon [_thread_blocked, id=138247, stack(0x000070001099c000,0x0000700010a9c000) (1024K)]
  0x00007f80d3282200 JavaThread "DefaultDispatcher-worker-36" daemon [_thread_blocked, id=163591, stack(0x0000700010a9f000,0x0000700010b9f000) (1024K)]
  0x00007f80d749cc00 JavaThread "DefaultDispatcher-worker-37" daemon [_thread_blocked, id=167687, stack(0x0000700010ba2000,0x0000700010ca2000) (1024K)]
  0x00007f80d2c6ba00 JavaThread "DefaultDispatcher-worker-40" daemon [_thread_blocked, id=141575, stack(0x0000700010ca5000,0x0000700010da5000) (1024K)]
  0x00007f80d1ceb800 JavaThread "DefaultDispatcher-worker-39" daemon [_thread_blocked, id=167175, stack(0x0000700010da8000,0x0000700010ea8000) (1024K)]
  0x00007f80d1cec000 JavaThread "DefaultDispatcher-worker-38" daemon [_thread_blocked, id=161799, stack(0x0000700010eab000,0x0000700010fab000) (1024K)]
  0x00007f80d6363a00 JavaThread "DefaultDispatcher-worker-41" daemon [_thread_blocked, id=142855, stack(0x0000700010fae000,0x00007000110ae000) (1024K)]
  0x00007f80d1ced000 JavaThread "DefaultDispatcher-worker-42" daemon [_thread_blocked, id=111643, stack(0x00007000110b1000,0x00007000111b1000) (1024K)]
  0x00007f80d3840400 JavaThread "DefaultDispatcher-worker-43" daemon [_thread_blocked, id=165127, stack(0x00007000111b4000,0x00007000112b4000) (1024K)]
  0x00007f80d31f2800 JavaThread "DefaultDispatcher-worker-44" daemon [_thread_blocked, id=162823, stack(0x00007000112b7000,0x00007000113b7000) (1024K)]
  0x00007f80d71cfc00 JavaThread "DefaultDispatcher-worker-47" daemon [_thread_blocked, id=141063, stack(0x00007000113ba000,0x00007000114ba000) (1024K)]
  0x00007f80d7430800 JavaThread "DefaultDispatcher-worker-46" daemon [_thread_blocked, id=161031, stack(0x00007000114bd000,0x00007000115bd000) (1024K)]
  0x00007f80d7431000 JavaThread "DefaultDispatcher-worker-45" daemon [_thread_blocked, id=144903, stack(0x00007000115c0000,0x00007000116c0000) (1024K)]
  0x00007f80d18e7c00 JavaThread "DefaultDispatcher-worker-49" daemon [_thread_blocked, id=161543, stack(0x00007000116c3000,0x00007000117c3000) (1024K)]
  0x00007f80d31f3800 JavaThread "DefaultDispatcher-worker-51" daemon [_thread_blocked, id=167431, stack(0x00007000117c6000,0x00007000118c6000) (1024K)]
  0x00007f80d30dee00 JavaThread "DefaultDispatcher-worker-50" daemon [_thread_blocked, id=145927, stack(0x00007000118c9000,0x00007000119c9000) (1024K)]
  0x00007f80d72bf600 JavaThread "DefaultDispatcher-worker-48" daemon [_thread_blocked, id=166919, stack(0x00007000119cc000,0x0000700011acc000) (1024K)]
  0x00007f80d18ea400 JavaThread "DefaultDispatcher-worker-52" daemon [_thread_blocked, id=142087, stack(0x0000700011acf000,0x0000700011bcf000) (1024K)]
  0x00007f80d30e0a00 JavaThread "DefaultDispatcher-worker-53" daemon [_thread_blocked, id=145671, stack(0x0000700011bd2000,0x0000700011cd2000) (1024K)]
  0x00007f80d32a5600 JavaThread "DefaultDispatcher-worker-59" daemon [_thread_blocked, id=161287, stack(0x0000700011cd5000,0x0000700011dd5000) (1024K)]
  0x00007f80d18eb400 JavaThread "DefaultDispatcher-worker-60" daemon [_thread_blocked, id=210435, stack(0x0000700011dd8000,0x0000700011ed8000) (1024K)]
  0x00007f80d2993600 JavaThread "DefaultDispatcher-worker-61" daemon [_thread_blocked, id=210179, stack(0x0000700011edb000,0x0000700011fdb000) (1024K)]
  0x00007f80d6054000 JavaThread "DefaultDispatcher-worker-62" daemon [_thread_blocked, id=180739, stack(0x0000700011fde000,0x00007000120de000) (1024K)]
  0x00007f80d4a64a00 JavaThread "DefaultDispatcher-worker-63" daemon [_thread_blocked, id=209923, stack(0x00007000120e1000,0x00007000121e1000) (1024K)]
  0x00007f80d3ae0600 JavaThread "DefaultDispatcher-worker-64" daemon [_thread_blocked, id=181251, stack(0x00007000121e4000,0x00007000122e4000) (1024K)]
  0x00007f80d748a000 JavaThread "DefaultDispatcher-worker-65" daemon [_thread_blocked, id=181507, stack(0x00007000122e7000,0x00007000123e7000) (1024K)]
  0x00007f80d3ae0e00 JavaThread "DefaultDispatcher-worker-66" daemon [_thread_blocked, id=209155, stack(0x00007000123ea000,0x00007000124ea000) (1024K)]
  0x00007f80d3bbfa00 JavaThread "DefaultDispatcher-worker-67" daemon [_thread_blocked, id=208643, stack(0x00007000124ed000,0x00007000125ed000) (1024K)]
  0x00007f80d1cf6a00 JavaThread "DefaultDispatcher-worker-57" daemon [_thread_blocked, id=208387, stack(0x00007000125f0000,0x00007000126f0000) (1024K)]
  0x00007f80d32f3c00 JavaThread "DefaultDispatcher-worker-58" daemon [_thread_blocked, id=182531, stack(0x00007000126f3000,0x00007000127f3000) (1024K)]
  0x00007f80d49faa00 JavaThread "DefaultDispatcher-worker-54" daemon [_thread_blocked, id=208131, stack(0x00007000127f6000,0x00007000128f6000) (1024K)]
  0x00007f80d49fb200 JavaThread "DefaultDispatcher-worker-55" daemon [_thread_blocked, id=207619, stack(0x00007000128f9000,0x00007000129f9000) (1024K)]
  0x00007f80d6119400 JavaThread "DefaultDispatcher-worker-56" daemon [_thread_blocked, id=207111, stack(0x00007000129fc000,0x0000700012afc000) (1024K)]
  0x00007f80d748b000 JavaThread "DefaultDispatcher-worker-68" daemon [_thread_blocked, id=206595, stack(0x0000700012aff000,0x0000700012bff000) (1024K)]
  0x00007f80d6119c00 JavaThread "DefaultDispatcher-worker-69" daemon [_thread_blocked, id=183299, stack(0x0000700012c02000,0x0000700012d02000) (1024K)]
  0x00007f80d1c9b000 JavaThread "DefaultDispatcher-worker-70" daemon [_thread_blocked, id=206083, stack(0x0000700012d05000,0x0000700012e05000) (1024K)]
  0x00007f80d2b3ea00 JavaThread "DefaultDispatcher-worker-71" daemon [_thread_blocked, id=205571, stack(0x0000700012e08000,0x0000700012f08000) (1024K)]
  0x00007f80d2908200 JavaThread "FileSystemWatcher"          daemon [_thread_blocked, id=174107, stack(0x0000700013094000,0x0000700013194000) (1024K)]
  0x00007f80d1cf6000 JavaThread "process reaper (pid 33816)" daemon [_thread_in_native, id=201235, stack(0x0000700013197000,0x00007000131bb000) (144K)]
  0x00007f80d4bb4c00 JavaThread "BaseDataReader: output stream of node"        [_thread_in_native, id=200967, stack(0x00007000131be000,0x00007000132be000) (1024K)]
  0x00007f80d4454600 JavaThread "BaseDataReader: error stream of node"        [_thread_in_native, id=200707, stack(0x00007000132c1000,0x00007000133c1000) (1024K)]
  0x00007f80d45c2200 JavaThread "I/O pool 16"                       [_thread_blocked, id=165427, stack(0x00007000133c4000,0x00007000134c4000) (1024K)]
  0x00007f80d2bfe600 JavaThread "Keep-Alive-Timer"           daemon [_thread_blocked, id=99623, stack(0x000070000d57a000,0x000070000d67a000) (1024K)]
  0x00007f80d1906800 JavaThread "JobScheduler FJ pool 0/7"   daemon [_thread_blocked, id=203307, stack(0x000070000e42a000,0x000070000e52a000) (1024K)]
  0x00007f80d4bee800 JavaThread "JobScheduler FJ pool 1/7"   daemon [_thread_blocked, id=149807, stack(0x000070000db0f000,0x000070000dc0f000) (1024K)]
  0x00007f80d61a9400 JavaThread "JobScheduler FJ pool 2/7"   daemon [_thread_blocked, id=152583, stack(0x000070000fc75000,0x000070000fd75000) (1024K)]
  0x00007f80d3c9ee00 JavaThread "JobScheduler FJ pool 3/7"   daemon [_thread_blocked, id=178439, stack(0x000070000fd78000,0x000070000fe78000) (1024K)]
  0x00007f80d6116a00 JavaThread "JobScheduler FJ pool 4/7"   daemon [_thread_blocked, id=152327, stack(0x00007000134c7000,0x00007000135c7000) (1024K)]
  0x00007f80d7407600 JavaThread "JobScheduler FJ pool 5/7"   daemon [_thread_blocked, id=211463, stack(0x00007000135ca000,0x00007000136ca000) (1024K)]
  0x00007f80d18e9800 JavaThread "JobScheduler FJ pool 6/7"   daemon [_thread_blocked, id=150023, stack(0x00007000136cd000,0x00007000137cd000) (1024K)]
  0x00007f80d38db600 JavaThread "HttpClient-4-SelectorManager" daemon [_thread_in_native, id=177971, stack(0x00007000137d0000,0x00007000138d0000) (1024K)]
  0x00007f80d4093000 JavaThread "HttpClient-5-SelectorManager" daemon [_thread_in_native, id=151119, stack(0x00007000138d3000,0x00007000139d3000) (1024K)]
Total: 136

Other Threads:
  0x00007f80d2005b40 VMThread "VM Thread"                           [id=21251, stack(0x000070000a743000,0x000070000a843000) (1024K)]
  0x00007f80d20050c0 WatcherThread "VM Periodic Task Thread"        [id=16387, stack(0x000070000a640000,0x000070000a740000) (1024K)]
  0x00007f80d2107470 WorkerThread "GC Thread#0"                     [id=14851, stack(0x000070000a131000,0x000070000a231000) (1024K)]
  0x00007f80d261bff0 WorkerThread "GC Thread#1"                     [id=35587, stack(0x000070000c091000,0x000070000c191000) (1024K)]
  0x00007f80d232b270 WorkerThread "GC Thread#2"                     [id=36099, stack(0x000070000c194000,0x000070000c294000) (1024K)]
  0x00007f80d241a3e0 WorkerThread "GC Thread#3"                     [id=39683, stack(0x000070000c297000,0x000070000c397000) (1024K)]
  0x00007f80d213a830 WorkerThread "GC Thread#4"                     [id=46351, stack(0x000070000c6a3000,0x000070000c7a3000) (1024K)]
  0x00007f80d2332050 WorkerThread "GC Thread#5"                     [id=63491, stack(0x000070000c7a6000,0x000070000c8a6000) (1024K)]
  0x00007f80d23327e0 WorkerThread "GC Thread#6"                     [id=47363, stack(0x000070000c8a9000,0x000070000c9a9000) (1024K)]
  0x00007f80d233a6d0 WorkerThread "GC Thread#7"                     [id=76815, stack(0x000070000cc5f000,0x000070000cd5f000) (1024K)]
  0x00007f80d2107c10 ConcurrentGCThread "G1 Main Marker"            [id=14339, stack(0x000070000a234000,0x000070000a334000) (1024K)]
  0x00007f80d2108580 WorkerThread "G1 Conc#0"                       [id=11779, stack(0x000070000a337000,0x000070000a437000) (1024K)]
  0x00007f80d2357a90 WorkerThread "G1 Conc#1"                       [id=126979, stack(0x000070000d06b000,0x000070000d16b000) (1024K)]
  0x00007f80d400e200 ConcurrentGCThread "G1 Refine#0"               [id=12035, stack(0x000070000a43a000,0x000070000a53a000) (1024K)]
  0x00007f80d2604720 ConcurrentGCThread "G1 Service"                [id=13315, stack(0x000070000a53d000,0x000070000a63d000) (1024K)]
Total: 15

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000780000000, size: 2048 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000130000000-0x0000000130d27000-0x0000000130d27000), size 13791232, SharedBaseAddress: 0x0000000130000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000000131000000-0x0000000171000000, reserved size: 1073741824
Narrow klass base: 0x0000000130000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 8192M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 1M
 Heap Min Capacity: 128M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 495616K, used 358195K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 75 young (76800K), 14 survivors (14336K)
 Metaspace       used 297286K, committed 301504K, reserved 1376256K
  class space    used 37045K, committed 38848K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000780000000, 0x0000000780100000, 0x0000000780100000|100%|HS|  |TAMS 0x0000000780000000| PB 0x0000000780000000| Complete 
|   1|0x0000000780100000, 0x0000000780200000, 0x0000000780200000|100%|HS|  |TAMS 0x0000000780100000| PB 0x0000000780100000| Complete 
|   2|0x0000000780200000, 0x0000000780300000, 0x0000000780300000|100%|HS|  |TAMS 0x0000000780200000| PB 0x0000000780200000| Complete 
|   3|0x0000000780300000, 0x0000000780400000, 0x0000000780400000|100%|HS|  |TAMS 0x0000000780300000| PB 0x0000000780300000| Complete 
|   4|0x0000000780400000, 0x0000000780500000, 0x0000000780500000|100%|HC|  |TAMS 0x0000000780400000| PB 0x0000000780400000| Complete 
|   5|0x0000000780500000, 0x0000000780600000, 0x0000000780600000|100%|HS|  |TAMS 0x0000000780500000| PB 0x0000000780500000| Complete 
|   6|0x0000000780600000, 0x0000000780700000, 0x0000000780700000|100%|HC|  |TAMS 0x0000000780600000| PB 0x0000000780600000| Complete 
|   7|0x0000000780700000, 0x0000000780800000, 0x0000000780800000|100%|HS|  |TAMS 0x0000000780700000| PB 0x0000000780700000| Complete 
|   8|0x0000000780800000, 0x0000000780900000, 0x0000000780900000|100%|HS|  |TAMS 0x0000000780800000| PB 0x0000000780800000| Complete 
|   9|0x0000000780900000, 0x0000000780a00000, 0x0000000780a00000|100%| O|  |TAMS 0x0000000780900000| PB 0x0000000780900000| Untracked 
|  10|0x0000000780a00000, 0x0000000780b00000, 0x0000000780b00000|100%| O|  |TAMS 0x0000000780a00000| PB 0x0000000780a00000| Untracked 
|  11|0x0000000780b00000, 0x0000000780c00000, 0x0000000780c00000|100%| O|  |TAMS 0x0000000780b00000| PB 0x0000000780b00000| Untracked 
|  12|0x0000000780c00000, 0x0000000780d00000, 0x0000000780d00000|100%| O|  |TAMS 0x0000000780c00000| PB 0x0000000780c00000| Untracked 
|  13|0x0000000780d00000, 0x0000000780e00000, 0x0000000780e00000|100%| O|  |TAMS 0x0000000780d00000| PB 0x0000000780d00000| Untracked 
|  14|0x0000000780e00000, 0x0000000780f00000, 0x0000000780f00000|100%| O|  |TAMS 0x0000000780e00000| PB 0x0000000780e00000| Untracked 
|  15|0x0000000780f00000, 0x0000000781000000, 0x0000000781000000|100%| O|  |TAMS 0x0000000780f00000| PB 0x0000000780f00000| Untracked 
|  16|0x0000000781000000, 0x0000000781100000, 0x0000000781100000|100%| O|  |TAMS 0x0000000781000000| PB 0x0000000781000000| Untracked 
|  17|0x0000000781100000, 0x0000000781200000, 0x0000000781200000|100%| O|  |TAMS 0x0000000781100000| PB 0x0000000781100000| Untracked 
|  18|0x0000000781200000, 0x0000000781300000, 0x0000000781300000|100%|HS|  |TAMS 0x0000000781200000| PB 0x0000000781200000| Complete 
|  19|0x0000000781300000, 0x0000000781400000, 0x0000000781400000|100%| O|  |TAMS 0x0000000781300000| PB 0x0000000781300000| Untracked 
|  20|0x0000000781400000, 0x0000000781500000, 0x0000000781500000|100%| O|  |TAMS 0x0000000781400000| PB 0x0000000781400000| Untracked 
|  21|0x0000000781500000, 0x0000000781600000, 0x0000000781600000|100%| O|  |TAMS 0x0000000781500000| PB 0x0000000781500000| Untracked 
|  22|0x0000000781600000, 0x0000000781700000, 0x0000000781700000|100%| O|  |TAMS 0x0000000781600000| PB 0x0000000781600000| Untracked 
|  23|0x0000000781700000, 0x0000000781800000, 0x0000000781800000|100%| O|  |TAMS 0x0000000781700000| PB 0x0000000781700000| Untracked 
|  24|0x0000000781800000, 0x0000000781900000, 0x0000000781900000|100%| O|  |TAMS 0x0000000781800000| PB 0x0000000781800000| Untracked 
|  25|0x0000000781900000, 0x0000000781a00000, 0x0000000781a00000|100%| O|  |TAMS 0x0000000781900000| PB 0x0000000781900000| Untracked 
|  26|0x0000000781a00000, 0x0000000781b00000, 0x0000000781b00000|100%| O|  |TAMS 0x0000000781a00000| PB 0x0000000781a00000| Untracked 
|  27|0x0000000781b00000, 0x0000000781c00000, 0x0000000781c00000|100%| O|  |TAMS 0x0000000781b00000| PB 0x0000000781b00000| Untracked 
|  28|0x0000000781c00000, 0x0000000781d00000, 0x0000000781d00000|100%| O|  |TAMS 0x0000000781c00000| PB 0x0000000781c00000| Untracked 
|  29|0x0000000781d00000, 0x0000000781e00000, 0x0000000781e00000|100%| O|  |TAMS 0x0000000781d00000| PB 0x0000000781d00000| Untracked 
|  30|0x0000000781e00000, 0x0000000781f00000, 0x0000000781f00000|100%| O|  |TAMS 0x0000000781e00000| PB 0x0000000781e00000| Untracked 
|  31|0x0000000781f00000, 0x0000000782000000, 0x0000000782000000|100%| O|  |TAMS 0x0000000781f00000| PB 0x0000000781f00000| Untracked 
|  32|0x0000000782000000, 0x0000000782100000, 0x0000000782100000|100%| O|  |TAMS 0x0000000782000000| PB 0x0000000782000000| Untracked 
|  33|0x0000000782100000, 0x0000000782200000, 0x0000000782200000|100%| O|  |TAMS 0x0000000782100000| PB 0x0000000782100000| Untracked 
|  34|0x0000000782200000, 0x0000000782300000, 0x0000000782300000|100%| O|  |TAMS 0x0000000782200000| PB 0x0000000782200000| Untracked 
|  35|0x0000000782300000, 0x0000000782400000, 0x0000000782400000|100%| O|  |TAMS 0x0000000782300000| PB 0x0000000782300000| Untracked 
|  36|0x0000000782400000, 0x0000000782500000, 0x0000000782500000|100%| O|  |TAMS 0x0000000782400000| PB 0x0000000782400000| Untracked 
|  37|0x0000000782500000, 0x0000000782600000, 0x0000000782600000|100%| O|  |TAMS 0x0000000782500000| PB 0x0000000782500000| Untracked 
|  38|0x0000000782600000, 0x0000000782700000, 0x0000000782700000|100%| O|  |TAMS 0x0000000782600000| PB 0x0000000782600000| Untracked 
|  39|0x0000000782700000, 0x0000000782800000, 0x0000000782800000|100%| O|  |TAMS 0x0000000782700000| PB 0x0000000782700000| Untracked 
|  40|0x0000000782800000, 0x0000000782900000, 0x0000000782900000|100%| O|  |TAMS 0x0000000782800000| PB 0x0000000782800000| Untracked 
|  41|0x0000000782900000, 0x0000000782a00000, 0x0000000782a00000|100%| O|  |TAMS 0x0000000782900000| PB 0x0000000782900000| Untracked 
|  42|0x0000000782a00000, 0x0000000782b00000, 0x0000000782b00000|100%| O|  |TAMS 0x0000000782a00000| PB 0x0000000782a00000| Untracked 
|  43|0x0000000782b00000, 0x0000000782c00000, 0x0000000782c00000|100%| O|Cm|TAMS 0x0000000782b00000| PB 0x0000000782b00000| Complete 
|  44|0x0000000782c00000, 0x0000000782d00000, 0x0000000782d00000|100%| O|  |TAMS 0x0000000782c00000| PB 0x0000000782c00000| Untracked 
|  45|0x0000000782d00000, 0x0000000782e00000, 0x0000000782e00000|100%| O|  |TAMS 0x0000000782d00000| PB 0x0000000782d00000| Untracked 
|  46|0x0000000782e00000, 0x0000000782f00000, 0x0000000782f00000|100%| O|  |TAMS 0x0000000782e00000| PB 0x0000000782e00000| Untracked 
|  47|0x0000000782f00000, 0x0000000783000000, 0x0000000783000000|100%| O|  |TAMS 0x0000000782f00000| PB 0x0000000782f00000| Untracked 
|  48|0x0000000783000000, 0x0000000783100000, 0x0000000783100000|100%| O|  |TAMS 0x0000000783000000| PB 0x0000000783000000| Untracked 
|  49|0x0000000783100000, 0x0000000783200000, 0x0000000783200000|100%| O|  |TAMS 0x0000000783100000| PB 0x0000000783100000| Untracked 
|  50|0x0000000783200000, 0x0000000783300000, 0x0000000783300000|100%| O|  |TAMS 0x0000000783200000| PB 0x0000000783200000| Untracked 
|  51|0x0000000783300000, 0x0000000783400000, 0x0000000783400000|100%| O|  |TAMS 0x0000000783300000| PB 0x0000000783300000| Untracked 
|  52|0x0000000783400000, 0x0000000783500000, 0x0000000783500000|100%| O|  |TAMS 0x0000000783400000| PB 0x0000000783400000| Untracked 
|  53|0x0000000783500000, 0x0000000783600000, 0x0000000783600000|100%| O|  |TAMS 0x0000000783500000| PB 0x0000000783500000| Untracked 
|  54|0x0000000783600000, 0x0000000783700000, 0x0000000783700000|100%|HS|  |TAMS 0x0000000783600000| PB 0x0000000783600000| Complete 
|  55|0x0000000783700000, 0x0000000783800000, 0x0000000783800000|100%|HC|  |TAMS 0x0000000783700000| PB 0x0000000783700000| Complete 
|  56|0x0000000783800000, 0x0000000783900000, 0x0000000783900000|100%|HC|  |TAMS 0x0000000783800000| PB 0x0000000783800000| Complete 
|  57|0x0000000783900000, 0x0000000783a00000, 0x0000000783a00000|100%|HC|  |TAMS 0x0000000783900000| PB 0x0000000783900000| Complete 
|  58|0x0000000783a00000, 0x0000000783b00000, 0x0000000783b00000|100%|HC|  |TAMS 0x0000000783a00000| PB 0x0000000783a00000| Complete 
|  59|0x0000000783b00000, 0x0000000783c00000, 0x0000000783c00000|100%| O|  |TAMS 0x0000000783b00000| PB 0x0000000783b00000| Untracked 
|  60|0x0000000783c00000, 0x0000000783d00000, 0x0000000783d00000|100%| O|  |TAMS 0x0000000783c00000| PB 0x0000000783c00000| Untracked 
|  61|0x0000000783d00000, 0x0000000783e00000, 0x0000000783e00000|100%| O|  |TAMS 0x0000000783d00000| PB 0x0000000783d00000| Untracked 
|  62|0x0000000783e00000, 0x0000000783f00000, 0x0000000783f00000|100%| O|  |TAMS 0x0000000783e00000| PB 0x0000000783e00000| Untracked 
|  63|0x0000000783f00000, 0x0000000784000000, 0x0000000784000000|100%|HS|  |TAMS 0x0000000783f00000| PB 0x0000000783f00000| Complete 
|  64|0x0000000784000000, 0x0000000784100000, 0x0000000784100000|100%|HS|  |TAMS 0x0000000784000000| PB 0x0000000784000000| Complete 
|  65|0x0000000784100000, 0x0000000784200000, 0x0000000784200000|100%|HS|  |TAMS 0x0000000784100000| PB 0x0000000784100000| Complete 
|  66|0x0000000784200000, 0x0000000784300000, 0x0000000784300000|100%| O|  |TAMS 0x0000000784200000| PB 0x0000000784200000| Untracked 
|  67|0x0000000784300000, 0x0000000784400000, 0x0000000784400000|100%| O|  |TAMS 0x0000000784300000| PB 0x0000000784300000| Untracked 
|  68|0x0000000784400000, 0x0000000784500000, 0x0000000784500000|100%| O|  |TAMS 0x0000000784400000| PB 0x0000000784400000| Untracked 
|  69|0x0000000784500000, 0x0000000784600000, 0x0000000784600000|100%| O|  |TAMS 0x0000000784500000| PB 0x0000000784500000| Untracked 
|  70|0x0000000784600000, 0x0000000784700000, 0x0000000784700000|100%| O|  |TAMS 0x0000000784600000| PB 0x0000000784600000| Untracked 
|  71|0x0000000784700000, 0x0000000784800000, 0x0000000784800000|100%| O|  |TAMS 0x0000000784700000| PB 0x0000000784700000| Untracked 
|  72|0x0000000784800000, 0x0000000784900000, 0x0000000784900000|100%| O|  |TAMS 0x0000000784800000| PB 0x0000000784800000| Untracked 
|  73|0x0000000784900000, 0x0000000784a00000, 0x0000000784a00000|100%| O|  |TAMS 0x0000000784900000| PB 0x0000000784900000| Untracked 
|  74|0x0000000784a00000, 0x0000000784b00000, 0x0000000784b00000|100%| O|  |TAMS 0x0000000784a00000| PB 0x0000000784a00000| Untracked 
|  75|0x0000000784b00000, 0x0000000784c00000, 0x0000000784c00000|100%| O|  |TAMS 0x0000000784b00000| PB 0x0000000784b00000| Untracked 
|  76|0x0000000784c00000, 0x0000000784d00000, 0x0000000784d00000|100%| O|  |TAMS 0x0000000784c00000| PB 0x0000000784c00000| Untracked 
|  77|0x0000000784d00000, 0x0000000784e00000, 0x0000000784e00000|100%| O|  |TAMS 0x0000000784d00000| PB 0x0000000784d00000| Untracked 
|  78|0x0000000784e00000, 0x0000000784f00000, 0x0000000784f00000|100%| O|  |TAMS 0x0000000784e00000| PB 0x0000000784e00000| Untracked 
|  79|0x0000000784f00000, 0x0000000785000000, 0x0000000785000000|100%| O|  |TAMS 0x0000000784f00000| PB 0x0000000784f00000| Untracked 
|  80|0x0000000785000000, 0x0000000785100000, 0x0000000785100000|100%| O|  |TAMS 0x0000000785000000| PB 0x0000000785000000| Untracked 
|  81|0x0000000785100000, 0x0000000785200000, 0x0000000785200000|100%| O|  |TAMS 0x0000000785100000| PB 0x0000000785100000| Untracked 
|  82|0x0000000785200000, 0x0000000785300000, 0x0000000785300000|100%| O|  |TAMS 0x0000000785200000| PB 0x0000000785200000| Untracked 
|  83|0x0000000785300000, 0x0000000785400000, 0x0000000785400000|100%| O|  |TAMS 0x0000000785300000| PB 0x0000000785300000| Untracked 
|  84|0x0000000785400000, 0x0000000785500000, 0x0000000785500000|100%| O|  |TAMS 0x0000000785400000| PB 0x0000000785400000| Untracked 
|  85|0x0000000785500000, 0x0000000785600000, 0x0000000785600000|100%| O|  |TAMS 0x0000000785500000| PB 0x0000000785500000| Untracked 
|  86|0x0000000785600000, 0x0000000785700000, 0x0000000785700000|100%| O|  |TAMS 0x0000000785600000| PB 0x0000000785600000| Untracked 
|  87|0x0000000785700000, 0x0000000785800000, 0x0000000785800000|100%| O|  |TAMS 0x0000000785700000| PB 0x0000000785700000| Untracked 
|  88|0x0000000785800000, 0x0000000785900000, 0x0000000785900000|100%| O|  |TAMS 0x0000000785800000| PB 0x0000000785800000| Untracked 
|  89|0x0000000785900000, 0x0000000785a00000, 0x0000000785a00000|100%| O|  |TAMS 0x0000000785900000| PB 0x0000000785900000| Untracked 
|  90|0x0000000785a00000, 0x0000000785b00000, 0x0000000785b00000|100%| O|  |TAMS 0x0000000785a00000| PB 0x0000000785a00000| Untracked 
|  91|0x0000000785b00000, 0x0000000785c00000, 0x0000000785c00000|100%| O|  |TAMS 0x0000000785b00000| PB 0x0000000785b00000| Untracked 
|  92|0x0000000785c00000, 0x0000000785d00000, 0x0000000785d00000|100%| O|  |TAMS 0x0000000785c00000| PB 0x0000000785c00000| Untracked 
|  93|0x0000000785d00000, 0x0000000785e00000, 0x0000000785e00000|100%| O|Cm|TAMS 0x0000000785d00000| PB 0x0000000785d00000| Complete 
|  94|0x0000000785e00000, 0x0000000785f00000, 0x0000000785f00000|100%| O|  |TAMS 0x0000000785e00000| PB 0x0000000785e00000| Untracked 
|  95|0x0000000785f00000, 0x0000000786000000, 0x0000000786000000|100%| O|  |TAMS 0x0000000785f00000| PB 0x0000000785f00000| Untracked 
|  96|0x0000000786000000, 0x0000000786100000, 0x0000000786100000|100%| O|  |TAMS 0x0000000786000000| PB 0x0000000786000000| Untracked 
|  97|0x0000000786100000, 0x0000000786200000, 0x0000000786200000|100%| O|  |TAMS 0x0000000786100000| PB 0x0000000786100000| Untracked 
|  98|0x0000000786200000, 0x0000000786300000, 0x0000000786300000|100%| O|  |TAMS 0x0000000786200000| PB 0x0000000786200000| Untracked 
|  99|0x0000000786300000, 0x0000000786400000, 0x0000000786400000|100%| O|  |TAMS 0x0000000786300000| PB 0x0000000786300000| Untracked 
| 100|0x0000000786400000, 0x0000000786500000, 0x0000000786500000|100%| O|  |TAMS 0x0000000786400000| PB 0x0000000786400000| Untracked 
| 101|0x0000000786500000, 0x0000000786600000, 0x0000000786600000|100%| O|  |TAMS 0x0000000786500000| PB 0x0000000786500000| Untracked 
| 102|0x0000000786600000, 0x0000000786700000, 0x0000000786700000|100%| O|  |TAMS 0x0000000786600000| PB 0x0000000786600000| Untracked 
| 103|0x0000000786700000, 0x0000000786800000, 0x0000000786800000|100%| O|  |TAMS 0x0000000786700000| PB 0x0000000786700000| Untracked 
| 104|0x0000000786800000, 0x0000000786900000, 0x0000000786900000|100%| O|  |TAMS 0x0000000786800000| PB 0x0000000786800000| Untracked 
| 105|0x0000000786900000, 0x0000000786a00000, 0x0000000786a00000|100%| O|  |TAMS 0x0000000786900000| PB 0x0000000786900000| Untracked 
| 106|0x0000000786a00000, 0x0000000786b00000, 0x0000000786b00000|100%| O|  |TAMS 0x0000000786a00000| PB 0x0000000786a00000| Untracked 
| 107|0x0000000786b00000, 0x0000000786c00000, 0x0000000786c00000|100%| O|  |TAMS 0x0000000786b00000| PB 0x0000000786b00000| Untracked 
| 108|0x0000000786c00000, 0x0000000786d00000, 0x0000000786d00000|100%| O|  |TAMS 0x0000000786c00000| PB 0x0000000786c00000| Untracked 
| 109|0x0000000786d00000, 0x0000000786e00000, 0x0000000786e00000|100%| O|  |TAMS 0x0000000786d00000| PB 0x0000000786d00000| Untracked 
| 110|0x0000000786e00000, 0x0000000786f00000, 0x0000000786f00000|100%| O|  |TAMS 0x0000000786e00000| PB 0x0000000786e00000| Untracked 
| 111|0x0000000786f00000, 0x0000000787000000, 0x0000000787000000|100%| O|  |TAMS 0x0000000786f00000| PB 0x0000000786f00000| Untracked 
| 112|0x0000000787000000, 0x0000000787100000, 0x0000000787100000|100%| O|  |TAMS 0x0000000787000000| PB 0x0000000787000000| Untracked 
| 113|0x0000000787100000, 0x0000000787200000, 0x0000000787200000|100%| O|  |TAMS 0x0000000787100000| PB 0x0000000787100000| Untracked 
| 114|0x0000000787200000, 0x0000000787300000, 0x0000000787300000|100%| O|Cm|TAMS 0x0000000787200000| PB 0x0000000787200000| Complete 
| 115|0x0000000787300000, 0x0000000787400000, 0x0000000787400000|100%| O|  |TAMS 0x0000000787300000| PB 0x0000000787300000| Untracked 
| 116|0x0000000787400000, 0x0000000787500000, 0x0000000787500000|100%| O|  |TAMS 0x0000000787400000| PB 0x0000000787400000| Untracked 
| 117|0x0000000787500000, 0x0000000787600000, 0x0000000787600000|100%| O|  |TAMS 0x0000000787500000| PB 0x0000000787500000| Untracked 
| 118|0x0000000787600000, 0x0000000787700000, 0x0000000787700000|100%| O|  |TAMS 0x0000000787600000| PB 0x0000000787600000| Untracked 
| 119|0x0000000787700000, 0x0000000787800000, 0x0000000787800000|100%| O|  |TAMS 0x0000000787700000| PB 0x0000000787700000| Untracked 
| 120|0x0000000787800000, 0x0000000787900000, 0x0000000787900000|100%| O|  |TAMS 0x0000000787800000| PB 0x0000000787800000| Untracked 
| 121|0x0000000787900000, 0x0000000787a00000, 0x0000000787a00000|100%| O|Cm|TAMS 0x0000000787900000| PB 0x0000000787900000| Complete 
| 122|0x0000000787a00000, 0x0000000787b00000, 0x0000000787b00000|100%| O|  |TAMS 0x0000000787a00000| PB 0x0000000787a00000| Untracked 
| 123|0x0000000787b00000, 0x0000000787c00000, 0x0000000787c00000|100%| O|  |TAMS 0x0000000787b00000| PB 0x0000000787b00000| Untracked 
| 124|0x0000000787c00000, 0x0000000787d00000, 0x0000000787d00000|100%| O|  |TAMS 0x0000000787c00000| PB 0x0000000787c00000| Untracked 
| 125|0x0000000787d00000, 0x0000000787e00000, 0x0000000787e00000|100%| O|  |TAMS 0x0000000787d00000| PB 0x0000000787d00000| Untracked 
| 126|0x0000000787e00000, 0x0000000787f00000, 0x0000000787f00000|100%|HS|  |TAMS 0x0000000787e00000| PB 0x0000000787e00000| Complete 
| 127|0x0000000787f00000, 0x0000000788000000, 0x0000000788000000|100%|HC|  |TAMS 0x0000000787f00000| PB 0x0000000787f00000| Complete 
| 128|0x0000000788000000, 0x0000000788100000, 0x0000000788100000|100%| O|  |TAMS 0x0000000788000000| PB 0x0000000788000000| Untracked 
| 129|0x0000000788100000, 0x0000000788200000, 0x0000000788200000|100%| O|  |TAMS 0x0000000788100000| PB 0x0000000788100000| Untracked 
| 130|0x0000000788200000, 0x0000000788300000, 0x0000000788300000|100%| O|  |TAMS 0x0000000788200000| PB 0x0000000788200000| Untracked 
| 131|0x0000000788300000, 0x0000000788400000, 0x0000000788400000|100%| O|  |TAMS 0x0000000788300000| PB 0x0000000788300000| Untracked 
| 132|0x0000000788400000, 0x0000000788500000, 0x0000000788500000|100%| O|Cm|TAMS 0x0000000788400000| PB 0x0000000788400000| Complete 
| 133|0x0000000788500000, 0x0000000788600000, 0x0000000788600000|100%| O|Cm|TAMS 0x0000000788500000| PB 0x0000000788500000| Complete 
| 134|0x0000000788600000, 0x0000000788700000, 0x0000000788700000|100%|HS|  |TAMS 0x0000000788600000| PB 0x0000000788600000| Complete 
| 135|0x0000000788700000, 0x0000000788800000, 0x0000000788800000|100%| O|  |TAMS 0x0000000788700000| PB 0x0000000788700000| Untracked 
| 136|0x0000000788800000, 0x0000000788900000, 0x0000000788900000|100%| O|  |TAMS 0x0000000788800000| PB 0x0000000788800000| Untracked 
| 137|0x0000000788900000, 0x0000000788a00000, 0x0000000788a00000|100%|HS|  |TAMS 0x0000000788900000| PB 0x0000000788900000| Complete 
| 138|0x0000000788a00000, 0x0000000788b00000, 0x0000000788b00000|100%| O|  |TAMS 0x0000000788a00000| PB 0x0000000788a00000| Untracked 
| 139|0x0000000788b00000, 0x0000000788c00000, 0x0000000788c00000|100%| O|  |TAMS 0x0000000788b00000| PB 0x0000000788b00000| Untracked 
| 140|0x0000000788c00000, 0x0000000788d00000, 0x0000000788d00000|100%| O|Cm|TAMS 0x0000000788c00000| PB 0x0000000788c00000| Complete 
| 141|0x0000000788d00000, 0x0000000788e00000, 0x0000000788e00000|100%| O|  |TAMS 0x0000000788d00000| PB 0x0000000788d00000| Untracked 
| 142|0x0000000788e00000, 0x0000000788f00000, 0x0000000788f00000|100%| O|  |TAMS 0x0000000788e00000| PB 0x0000000788e00000| Untracked 
| 143|0x0000000788f00000, 0x0000000789000000, 0x0000000789000000|100%| O|Cm|TAMS 0x0000000788f00000| PB 0x0000000788f00000| Complete 
| 144|0x0000000789000000, 0x0000000789100000, 0x0000000789100000|100%| O|Cm|TAMS 0x0000000789000000| PB 0x0000000789000000| Complete 
| 145|0x0000000789100000, 0x0000000789200000, 0x0000000789200000|100%| O|  |TAMS 0x0000000789100000| PB 0x0000000789100000| Untracked 
| 146|0x0000000789200000, 0x0000000789300000, 0x0000000789300000|100%| O|  |TAMS 0x0000000789200000| PB 0x0000000789200000| Untracked 
| 147|0x0000000789300000, 0x0000000789400000, 0x0000000789400000|100%| O|  |TAMS 0x0000000789300000| PB 0x0000000789300000| Untracked 
| 148|0x0000000789400000, 0x0000000789500000, 0x0000000789500000|100%| O|  |TAMS 0x0000000789400000| PB 0x0000000789400000| Untracked 
| 149|0x0000000789500000, 0x0000000789600000, 0x0000000789600000|100%| O|  |TAMS 0x0000000789500000| PB 0x0000000789500000| Untracked 
| 150|0x0000000789600000, 0x0000000789700000, 0x0000000789700000|100%| O|  |TAMS 0x0000000789600000| PB 0x0000000789600000| Untracked 
| 151|0x0000000789700000, 0x0000000789800000, 0x0000000789800000|100%| O|  |TAMS 0x0000000789700000| PB 0x0000000789700000| Untracked 
| 152|0x0000000789800000, 0x0000000789900000, 0x0000000789900000|100%| O|  |TAMS 0x0000000789800000| PB 0x0000000789800000| Untracked 
| 153|0x0000000789900000, 0x0000000789a00000, 0x0000000789a00000|100%| O|  |TAMS 0x0000000789900000| PB 0x0000000789900000| Untracked 
| 154|0x0000000789a00000, 0x0000000789b00000, 0x0000000789b00000|100%| O|  |TAMS 0x0000000789a00000| PB 0x0000000789a00000| Untracked 
| 155|0x0000000789b00000, 0x0000000789c00000, 0x0000000789c00000|100%| O|  |TAMS 0x0000000789b00000| PB 0x0000000789b00000| Untracked 
| 156|0x0000000789c00000, 0x0000000789d00000, 0x0000000789d00000|100%| O|  |TAMS 0x0000000789c00000| PB 0x0000000789c00000| Untracked 
| 157|0x0000000789d00000, 0x0000000789e00000, 0x0000000789e00000|100%| O|  |TAMS 0x0000000789d00000| PB 0x0000000789d00000| Untracked 
| 158|0x0000000789e00000, 0x0000000789f00000, 0x0000000789f00000|100%| O|  |TAMS 0x0000000789e00000| PB 0x0000000789e00000| Untracked 
| 159|0x0000000789f00000, 0x000000078a000000, 0x000000078a000000|100%| O|  |TAMS 0x0000000789f00000| PB 0x0000000789f00000| Untracked 
| 160|0x000000078a000000, 0x000000078a100000, 0x000000078a100000|100%| O|  |TAMS 0x000000078a000000| PB 0x000000078a000000| Untracked 
| 161|0x000000078a100000, 0x000000078a200000, 0x000000078a200000|100%| O|  |TAMS 0x000000078a100000| PB 0x000000078a100000| Untracked 
| 162|0x000000078a200000, 0x000000078a300000, 0x000000078a300000|100%| O|  |TAMS 0x000000078a200000| PB 0x000000078a200000| Untracked 
| 163|0x000000078a300000, 0x000000078a400000, 0x000000078a400000|100%| O|  |TAMS 0x000000078a300000| PB 0x000000078a300000| Untracked 
| 164|0x000000078a400000, 0x000000078a500000, 0x000000078a500000|100%| O|  |TAMS 0x000000078a400000| PB 0x000000078a400000| Untracked 
| 165|0x000000078a500000, 0x000000078a600000, 0x000000078a600000|100%| O|  |TAMS 0x000000078a500000| PB 0x000000078a500000| Untracked 
| 166|0x000000078a600000, 0x000000078a700000, 0x000000078a700000|100%| O|  |TAMS 0x000000078a600000| PB 0x000000078a600000| Untracked 
| 167|0x000000078a700000, 0x000000078a800000, 0x000000078a800000|100%| O|  |TAMS 0x000000078a700000| PB 0x000000078a700000| Untracked 
| 168|0x000000078a800000, 0x000000078a900000, 0x000000078a900000|100%| O|  |TAMS 0x000000078a800000| PB 0x000000078a800000| Untracked 
| 169|0x000000078a900000, 0x000000078aa00000, 0x000000078aa00000|100%| O|  |TAMS 0x000000078a900000| PB 0x000000078a900000| Untracked 
| 170|0x000000078aa00000, 0x000000078ab00000, 0x000000078ab00000|100%| O|  |TAMS 0x000000078aa00000| PB 0x000000078aa00000| Untracked 
| 171|0x000000078ab00000, 0x000000078ac00000, 0x000000078ac00000|100%| O|  |TAMS 0x000000078ab00000| PB 0x000000078ab00000| Untracked 
| 172|0x000000078ac00000, 0x000000078ad00000, 0x000000078ad00000|100%| O|  |TAMS 0x000000078ac00000| PB 0x000000078ac00000| Untracked 
| 173|0x000000078ad00000, 0x000000078ae00000, 0x000000078ae00000|100%| O|  |TAMS 0x000000078ad00000| PB 0x000000078ad00000| Untracked 
| 174|0x000000078ae00000, 0x000000078af00000, 0x000000078af00000|100%| O|  |TAMS 0x000000078ae00000| PB 0x000000078ae00000| Untracked 
| 175|0x000000078af00000, 0x000000078b000000, 0x000000078b000000|100%| O|  |TAMS 0x000000078af00000| PB 0x000000078af00000| Untracked 
| 176|0x000000078b000000, 0x000000078b100000, 0x000000078b100000|100%| O|  |TAMS 0x000000078b000000| PB 0x000000078b000000| Untracked 
| 177|0x000000078b100000, 0x000000078b200000, 0x000000078b200000|100%| O|  |TAMS 0x000000078b100000| PB 0x000000078b100000| Untracked 
| 178|0x000000078b200000, 0x000000078b300000, 0x000000078b300000|100%| O|  |TAMS 0x000000078b200000| PB 0x000000078b200000| Untracked 
| 179|0x000000078b300000, 0x000000078b400000, 0x000000078b400000|100%| O|  |TAMS 0x000000078b300000| PB 0x000000078b300000| Untracked 
| 180|0x000000078b400000, 0x000000078b500000, 0x000000078b500000|100%| O|  |TAMS 0x000000078b400000| PB 0x000000078b400000| Untracked 
| 181|0x000000078b500000, 0x000000078b600000, 0x000000078b600000|100%| O|  |TAMS 0x000000078b500000| PB 0x000000078b500000| Untracked 
| 182|0x000000078b600000, 0x000000078b700000, 0x000000078b700000|100%| O|  |TAMS 0x000000078b600000| PB 0x000000078b600000| Untracked 
| 183|0x000000078b700000, 0x000000078b800000, 0x000000078b800000|100%| O|  |TAMS 0x000000078b700000| PB 0x000000078b700000| Untracked 
| 184|0x000000078b800000, 0x000000078b900000, 0x000000078b900000|100%| O|  |TAMS 0x000000078b800000| PB 0x000000078b800000| Untracked 
| 185|0x000000078b900000, 0x000000078ba00000, 0x000000078ba00000|100%| O|  |TAMS 0x000000078b900000| PB 0x000000078b900000| Untracked 
| 186|0x000000078ba00000, 0x000000078bb00000, 0x000000078bb00000|100%| O|  |TAMS 0x000000078ba00000| PB 0x000000078ba00000| Untracked 
| 187|0x000000078bb00000, 0x000000078bc00000, 0x000000078bc00000|100%| O|  |TAMS 0x000000078bb00000| PB 0x000000078bb00000| Untracked 
| 188|0x000000078bc00000, 0x000000078bd00000, 0x000000078bd00000|100%| O|  |TAMS 0x000000078bc00000| PB 0x000000078bc00000| Untracked 
| 189|0x000000078bd00000, 0x000000078be00000, 0x000000078be00000|100%| O|  |TAMS 0x000000078bd00000| PB 0x000000078bd00000| Untracked 
| 190|0x000000078be00000, 0x000000078bf00000, 0x000000078bf00000|100%| O|  |TAMS 0x000000078be00000| PB 0x000000078be00000| Untracked 
| 191|0x000000078bf00000, 0x000000078c000000, 0x000000078c000000|100%| O|  |TAMS 0x000000078bf00000| PB 0x000000078bf00000| Untracked 
| 192|0x000000078c000000, 0x000000078c100000, 0x000000078c100000|100%| O|  |TAMS 0x000000078c000000| PB 0x000000078c000000| Untracked 
| 193|0x000000078c100000, 0x000000078c100000, 0x000000078c200000|  0%| F|  |TAMS 0x000000078c100000| PB 0x000000078c100000| Untracked 
| 194|0x000000078c200000, 0x000000078c200000, 0x000000078c300000|  0%| F|  |TAMS 0x000000078c200000| PB 0x000000078c200000| Untracked 
| 195|0x000000078c300000, 0x000000078c400000, 0x000000078c400000|100%| O|  |TAMS 0x000000078c300000| PB 0x000000078c300000| Untracked 
| 196|0x000000078c400000, 0x000000078c500000, 0x000000078c500000|100%| O|  |TAMS 0x000000078c400000| PB 0x000000078c400000| Untracked 
| 197|0x000000078c500000, 0x000000078c600000, 0x000000078c600000|100%| O|  |TAMS 0x000000078c500000| PB 0x000000078c500000| Untracked 
| 198|0x000000078c600000, 0x000000078c700000, 0x000000078c700000|100%| O|Cm|TAMS 0x000000078c600000| PB 0x000000078c600000| Complete 
| 199|0x000000078c700000, 0x000000078c800000, 0x000000078c800000|100%| O|  |TAMS 0x000000078c700000| PB 0x000000078c700000| Untracked 
| 200|0x000000078c800000, 0x000000078c900000, 0x000000078c900000|100%| O|  |TAMS 0x000000078c800000| PB 0x000000078c800000| Untracked 
| 201|0x000000078c900000, 0x000000078ca00000, 0x000000078ca00000|100%| O|  |TAMS 0x000000078c900000| PB 0x000000078c900000| Untracked 
| 202|0x000000078ca00000, 0x000000078cb00000, 0x000000078cb00000|100%| O|  |TAMS 0x000000078ca00000| PB 0x000000078ca00000| Untracked 
| 203|0x000000078cb00000, 0x000000078cc00000, 0x000000078cc00000|100%| O|  |TAMS 0x000000078cb00000| PB 0x000000078cb00000| Untracked 
| 204|0x000000078cc00000, 0x000000078cd00000, 0x000000078cd00000|100%| O|  |TAMS 0x000000078cc00000| PB 0x000000078cc00000| Untracked 
| 205|0x000000078cd00000, 0x000000078ce00000, 0x000000078ce00000|100%| O|Cm|TAMS 0x000000078cd00000| PB 0x000000078cd00000| Complete 
| 206|0x000000078ce00000, 0x000000078cf00000, 0x000000078cf00000|100%| O|  |TAMS 0x000000078ce00000| PB 0x000000078ce00000| Untracked 
| 207|0x000000078cf00000, 0x000000078d000000, 0x000000078d000000|100%| O|  |TAMS 0x000000078cf00000| PB 0x000000078cf00000| Untracked 
| 208|0x000000078d000000, 0x000000078d100000, 0x000000078d100000|100%| O|  |TAMS 0x000000078d000000| PB 0x000000078d000000| Untracked 
| 209|0x000000078d100000, 0x000000078d200000, 0x000000078d200000|100%| O|  |TAMS 0x000000078d100000| PB 0x000000078d100000| Untracked 
| 210|0x000000078d200000, 0x000000078d300000, 0x000000078d300000|100%| O|Cm|TAMS 0x000000078d200000| PB 0x000000078d200000| Complete 
| 211|0x000000078d300000, 0x000000078d400000, 0x000000078d400000|100%| O|  |TAMS 0x000000078d300000| PB 0x000000078d300000| Untracked 
| 212|0x000000078d400000, 0x000000078d500000, 0x000000078d500000|100%| O|  |TAMS 0x000000078d400000| PB 0x000000078d400000| Untracked 
| 213|0x000000078d500000, 0x000000078d600000, 0x000000078d600000|100%| O|  |TAMS 0x000000078d500000| PB 0x000000078d500000| Untracked 
| 214|0x000000078d600000, 0x000000078d700000, 0x000000078d700000|100%| O|  |TAMS 0x000000078d600000| PB 0x000000078d600000| Untracked 
| 215|0x000000078d700000, 0x000000078d800000, 0x000000078d800000|100%| O|  |TAMS 0x000000078d700000| PB 0x000000078d700000| Untracked 
| 216|0x000000078d800000, 0x000000078d900000, 0x000000078d900000|100%| O|  |TAMS 0x000000078d800000| PB 0x000000078d800000| Untracked 
| 217|0x000000078d900000, 0x000000078da00000, 0x000000078da00000|100%| O|  |TAMS 0x000000078d900000| PB 0x000000078d900000| Untracked 
| 218|0x000000078da00000, 0x000000078db00000, 0x000000078db00000|100%| O|  |TAMS 0x000000078da00000| PB 0x000000078da00000| Untracked 
| 219|0x000000078db00000, 0x000000078dc00000, 0x000000078dc00000|100%| O|  |TAMS 0x000000078db00000| PB 0x000000078db00000| Untracked 
| 220|0x000000078dc00000, 0x000000078dd00000, 0x000000078dd00000|100%| O|  |TAMS 0x000000078dc00000| PB 0x000000078dc00000| Untracked 
| 221|0x000000078dd00000, 0x000000078de00000, 0x000000078de00000|100%| O|  |TAMS 0x000000078dd00000| PB 0x000000078dd00000| Untracked 
| 222|0x000000078de00000, 0x000000078df00000, 0x000000078df00000|100%| O|  |TAMS 0x000000078de00000| PB 0x000000078de00000| Untracked 
| 223|0x000000078df00000, 0x000000078e000000, 0x000000078e000000|100%| O|  |TAMS 0x000000078df00000| PB 0x000000078df00000| Untracked 
| 224|0x000000078e000000, 0x000000078e100000, 0x000000078e100000|100%| O|  |TAMS 0x000000078e000000| PB 0x000000078e000000| Untracked 
| 225|0x000000078e100000, 0x000000078e200000, 0x000000078e200000|100%| O|  |TAMS 0x000000078e100000| PB 0x000000078e100000| Untracked 
| 226|0x000000078e200000, 0x000000078e300000, 0x000000078e300000|100%| O|  |TAMS 0x000000078e200000| PB 0x000000078e200000| Untracked 
| 227|0x000000078e300000, 0x000000078e400000, 0x000000078e400000|100%| O|  |TAMS 0x000000078e300000| PB 0x000000078e300000| Untracked 
| 228|0x000000078e400000, 0x000000078e4f8760, 0x000000078e500000| 97%| O|  |TAMS 0x000000078e400000| PB 0x000000078e400000| Untracked 
| 229|0x000000078e500000, 0x000000078e600000, 0x000000078e600000|100%| O|  |TAMS 0x000000078e500000| PB 0x000000078e500000| Untracked 
| 230|0x000000078e600000, 0x000000078e700000, 0x000000078e700000|100%| O|  |TAMS 0x000000078e600000| PB 0x000000078e600000| Untracked 
| 231|0x000000078e700000, 0x000000078e800000, 0x000000078e800000|100%| O|  |TAMS 0x000000078e700000| PB 0x000000078e700000| Untracked 
| 232|0x000000078e800000, 0x000000078e900000, 0x000000078e900000|100%| O|  |TAMS 0x000000078e800000| PB 0x000000078e800000| Untracked 
| 233|0x000000078e900000, 0x000000078ea00000, 0x000000078ea00000|100%| O|  |TAMS 0x000000078e900000| PB 0x000000078e900000| Untracked 
| 234|0x000000078ea00000, 0x000000078eb00000, 0x000000078eb00000|100%| O|  |TAMS 0x000000078ea00000| PB 0x000000078ea00000| Untracked 
| 235|0x000000078eb00000, 0x000000078ec00000, 0x000000078ec00000|100%| O|  |TAMS 0x000000078eb00000| PB 0x000000078eb00000| Untracked 
| 236|0x000000078ec00000, 0x000000078ed00000, 0x000000078ed00000|100%| O|  |TAMS 0x000000078ec00000| PB 0x000000078ec00000| Untracked 
| 237|0x000000078ed00000, 0x000000078ee00000, 0x000000078ee00000|100%|HS|  |TAMS 0x000000078ed00000| PB 0x000000078ed00000| Complete 
| 238|0x000000078ee00000, 0x000000078ef00000, 0x000000078ef00000|100%| O|  |TAMS 0x000000078ee00000| PB 0x000000078ee00000| Untracked 
| 239|0x000000078ef00000, 0x000000078f000000, 0x000000078f000000|100%| O|  |TAMS 0x000000078ef00000| PB 0x000000078ef00000| Untracked 
| 240|0x000000078f000000, 0x000000078f100000, 0x000000078f100000|100%| O|  |TAMS 0x000000078f000000| PB 0x000000078f000000| Untracked 
| 241|0x000000078f100000, 0x000000078f200000, 0x000000078f200000|100%| O|  |TAMS 0x000000078f100000| PB 0x000000078f100000| Untracked 
| 242|0x000000078f200000, 0x000000078f300000, 0x000000078f300000|100%| O|  |TAMS 0x000000078f200000| PB 0x000000078f200000| Untracked 
| 243|0x000000078f300000, 0x000000078f400000, 0x000000078f400000|100%| O|  |TAMS 0x000000078f300000| PB 0x000000078f300000| Untracked 
| 244|0x000000078f400000, 0x000000078f500000, 0x000000078f500000|100%| O|  |TAMS 0x000000078f400000| PB 0x000000078f400000| Untracked 
| 245|0x000000078f500000, 0x000000078f600000, 0x000000078f600000|100%| O|  |TAMS 0x000000078f500000| PB 0x000000078f500000| Untracked 
| 246|0x000000078f600000, 0x000000078f700000, 0x000000078f700000|100%| O|  |TAMS 0x000000078f600000| PB 0x000000078f600000| Untracked 
| 247|0x000000078f700000, 0x000000078f800000, 0x000000078f800000|100%| O|  |TAMS 0x000000078f700000| PB 0x000000078f700000| Untracked 
| 248|0x000000078f800000, 0x000000078f900000, 0x000000078f900000|100%| O|  |TAMS 0x000000078f800000| PB 0x000000078f800000| Untracked 
| 249|0x000000078f900000, 0x000000078fa00000, 0x000000078fa00000|100%| O|  |TAMS 0x000000078f900000| PB 0x000000078f900000| Untracked 
| 250|0x000000078fa00000, 0x000000078fb00000, 0x000000078fb00000|100%| O|Cm|TAMS 0x000000078fa00000| PB 0x000000078fa00000| Complete 
| 251|0x000000078fb00000, 0x000000078fc00000, 0x000000078fc00000|100%| O|  |TAMS 0x000000078fb00000| PB 0x000000078fb00000| Untracked 
| 252|0x000000078fc00000, 0x000000078fd00000, 0x000000078fd00000|100%| O|Cm|TAMS 0x000000078fc00000| PB 0x000000078fc00000| Complete 
| 253|0x000000078fd00000, 0x000000078fe00000, 0x000000078fe00000|100%| O|Cm|TAMS 0x000000078fd00000| PB 0x000000078fd00000| Complete 
| 254|0x000000078fe00000, 0x000000078ff00000, 0x000000078ff00000|100%| O|Cm|TAMS 0x000000078fe00000| PB 0x000000078fe00000| Complete 
| 255|0x000000078ff00000, 0x0000000790000000, 0x0000000790000000|100%|HS|  |TAMS 0x000000078ff00000| PB 0x000000078ff00000| Complete 
| 256|0x0000000790000000, 0x0000000790100000, 0x0000000790100000|100%|HC|  |TAMS 0x0000000790000000| PB 0x0000000790000000| Complete 
| 257|0x0000000790100000, 0x0000000790200000, 0x0000000790200000|100%|HS|  |TAMS 0x0000000790100000| PB 0x0000000790100000| Complete 
| 258|0x0000000790200000, 0x0000000790200000, 0x0000000790300000|  0%| F|  |TAMS 0x0000000790200000| PB 0x0000000790200000| Untracked 
| 259|0x0000000790300000, 0x0000000790300000, 0x0000000790400000|  0%| F|  |TAMS 0x0000000790300000| PB 0x0000000790300000| Untracked 
| 260|0x0000000790400000, 0x0000000790400000, 0x0000000790500000|  0%| F|  |TAMS 0x0000000790400000| PB 0x0000000790400000| Untracked 
| 261|0x0000000790500000, 0x0000000790500000, 0x0000000790600000|  0%| F|  |TAMS 0x0000000790500000| PB 0x0000000790500000| Untracked 
| 262|0x0000000790600000, 0x0000000790600000, 0x0000000790700000|  0%| F|  |TAMS 0x0000000790600000| PB 0x0000000790600000| Untracked 
| 263|0x0000000790700000, 0x0000000790700000, 0x0000000790800000|  0%| F|  |TAMS 0x0000000790700000| PB 0x0000000790700000| Untracked 
| 264|0x0000000790800000, 0x0000000790800000, 0x0000000790900000|  0%| F|  |TAMS 0x0000000790800000| PB 0x0000000790800000| Untracked 
| 265|0x0000000790900000, 0x0000000790900000, 0x0000000790a00000|  0%| F|  |TAMS 0x0000000790900000| PB 0x0000000790900000| Untracked 
| 266|0x0000000790a00000, 0x0000000790a00000, 0x0000000790b00000|  0%| F|  |TAMS 0x0000000790a00000| PB 0x0000000790a00000| Untracked 
| 267|0x0000000790b00000, 0x0000000790b00000, 0x0000000790c00000|  0%| F|  |TAMS 0x0000000790b00000| PB 0x0000000790b00000| Untracked 
| 268|0x0000000790c00000, 0x0000000790c00000, 0x0000000790d00000|  0%| F|  |TAMS 0x0000000790c00000| PB 0x0000000790c00000| Untracked 
| 269|0x0000000790d00000, 0x0000000790d00000, 0x0000000790e00000|  0%| F|  |TAMS 0x0000000790d00000| PB 0x0000000790d00000| Untracked 
| 270|0x0000000790e00000, 0x0000000790e00000, 0x0000000790f00000|  0%| F|  |TAMS 0x0000000790e00000| PB 0x0000000790e00000| Untracked 
| 271|0x0000000790f00000, 0x0000000790f00000, 0x0000000791000000|  0%| F|  |TAMS 0x0000000790f00000| PB 0x0000000790f00000| Untracked 
| 272|0x0000000791000000, 0x0000000791000000, 0x0000000791100000|  0%| F|  |TAMS 0x0000000791000000| PB 0x0000000791000000| Untracked 
| 273|0x0000000791100000, 0x0000000791100000, 0x0000000791200000|  0%| F|  |TAMS 0x0000000791100000| PB 0x0000000791100000| Untracked 
| 274|0x0000000791200000, 0x0000000791200000, 0x0000000791300000|  0%| F|  |TAMS 0x0000000791200000| PB 0x0000000791200000| Untracked 
| 275|0x0000000791300000, 0x0000000791300000, 0x0000000791400000|  0%| F|  |TAMS 0x0000000791300000| PB 0x0000000791300000| Untracked 
| 276|0x0000000791400000, 0x00000007914d45f0, 0x0000000791500000| 82%| S|CS|TAMS 0x0000000791400000| PB 0x0000000791400000| Complete 
| 277|0x0000000791500000, 0x0000000791600000, 0x0000000791600000|100%| S|CS|TAMS 0x0000000791500000| PB 0x0000000791500000| Complete 
| 278|0x0000000791600000, 0x0000000791700000, 0x0000000791700000|100%| S|CS|TAMS 0x0000000791600000| PB 0x0000000791600000| Complete 
| 279|0x0000000791700000, 0x0000000791800000, 0x0000000791800000|100%| S|CS|TAMS 0x0000000791700000| PB 0x0000000791700000| Complete 
| 280|0x0000000791800000, 0x0000000791900000, 0x0000000791900000|100%| S|CS|TAMS 0x0000000791800000| PB 0x0000000791800000| Complete 
| 281|0x0000000791900000, 0x0000000791a00000, 0x0000000791a00000|100%| S|CS|TAMS 0x0000000791900000| PB 0x0000000791900000| Complete 
| 282|0x0000000791a00000, 0x0000000791b00000, 0x0000000791b00000|100%| S|CS|TAMS 0x0000000791a00000| PB 0x0000000791a00000| Complete 
| 283|0x0000000791b00000, 0x0000000791c00000, 0x0000000791c00000|100%| S|CS|TAMS 0x0000000791b00000| PB 0x0000000791b00000| Complete 
| 284|0x0000000791c00000, 0x0000000791d00000, 0x0000000791d00000|100%| S|CS|TAMS 0x0000000791c00000| PB 0x0000000791c00000| Complete 
| 285|0x0000000791d00000, 0x0000000791e00000, 0x0000000791e00000|100%| S|CS|TAMS 0x0000000791d00000| PB 0x0000000791d00000| Complete 
| 286|0x0000000791e00000, 0x0000000791e00000, 0x0000000791f00000|  0%| F|  |TAMS 0x0000000791e00000| PB 0x0000000791e00000| Untracked 
| 287|0x0000000791f00000, 0x0000000792000000, 0x0000000792000000|100%| S|CS|TAMS 0x0000000791f00000| PB 0x0000000791f00000| Complete 
| 288|0x0000000792000000, 0x0000000792100000, 0x0000000792100000|100%| S|CS|TAMS 0x0000000792000000| PB 0x0000000792000000| Complete 
| 289|0x0000000792100000, 0x0000000792200000, 0x0000000792200000|100%| S|CS|TAMS 0x0000000792100000| PB 0x0000000792100000| Complete 
| 290|0x0000000792200000, 0x0000000792300000, 0x0000000792300000|100%| S|CS|TAMS 0x0000000792200000| PB 0x0000000792200000| Complete 
| 291|0x0000000792300000, 0x0000000792300000, 0x0000000792400000|  0%| F|  |TAMS 0x0000000792300000| PB 0x0000000792300000| Untracked 
| 292|0x0000000792400000, 0x0000000792400000, 0x0000000792500000|  0%| F|  |TAMS 0x0000000792400000| PB 0x0000000792400000| Untracked 
| 293|0x0000000792500000, 0x0000000792500000, 0x0000000792600000|  0%| F|  |TAMS 0x0000000792500000| PB 0x0000000792500000| Untracked 
| 294|0x0000000792600000, 0x0000000792600000, 0x0000000792700000|  0%| F|  |TAMS 0x0000000792600000| PB 0x0000000792600000| Untracked 
| 295|0x0000000792700000, 0x0000000792700000, 0x0000000792800000|  0%| F|  |TAMS 0x0000000792700000| PB 0x0000000792700000| Untracked 
| 296|0x0000000792800000, 0x0000000792800000, 0x0000000792900000|  0%| F|  |TAMS 0x0000000792800000| PB 0x0000000792800000| Untracked 
| 297|0x0000000792900000, 0x0000000792900000, 0x0000000792a00000|  0%| F|  |TAMS 0x0000000792900000| PB 0x0000000792900000| Untracked 
| 298|0x0000000792a00000, 0x0000000792a00000, 0x0000000792b00000|  0%| F|  |TAMS 0x0000000792a00000| PB 0x0000000792a00000| Untracked 
| 299|0x0000000792b00000, 0x0000000792b00000, 0x0000000792c00000|  0%| F|  |TAMS 0x0000000792b00000| PB 0x0000000792b00000| Untracked 
| 300|0x0000000792c00000, 0x0000000792c00000, 0x0000000792d00000|  0%| F|  |TAMS 0x0000000792c00000| PB 0x0000000792c00000| Untracked 
| 301|0x0000000792d00000, 0x0000000792d00000, 0x0000000792e00000|  0%| F|  |TAMS 0x0000000792d00000| PB 0x0000000792d00000| Untracked 
| 302|0x0000000792e00000, 0x0000000792e00000, 0x0000000792f00000|  0%| F|  |TAMS 0x0000000792e00000| PB 0x0000000792e00000| Untracked 
| 303|0x0000000792f00000, 0x0000000792f00000, 0x0000000793000000|  0%| F|  |TAMS 0x0000000792f00000| PB 0x0000000792f00000| Untracked 
| 304|0x0000000793000000, 0x0000000793000000, 0x0000000793100000|  0%| F|  |TAMS 0x0000000793000000| PB 0x0000000793000000| Untracked 
| 305|0x0000000793100000, 0x0000000793100000, 0x0000000793200000|  0%| F|  |TAMS 0x0000000793100000| PB 0x0000000793100000| Untracked 
| 306|0x0000000793200000, 0x0000000793200000, 0x0000000793300000|  0%| F|  |TAMS 0x0000000793200000| PB 0x0000000793200000| Untracked 
| 307|0x0000000793300000, 0x0000000793300000, 0x0000000793400000|  0%| F|  |TAMS 0x0000000793300000| PB 0x0000000793300000| Untracked 
| 308|0x0000000793400000, 0x0000000793400000, 0x0000000793500000|  0%| F|  |TAMS 0x0000000793400000| PB 0x0000000793400000| Untracked 
| 309|0x0000000793500000, 0x0000000793500000, 0x0000000793600000|  0%| F|  |TAMS 0x0000000793500000| PB 0x0000000793500000| Untracked 
| 310|0x0000000793600000, 0x0000000793600000, 0x0000000793700000|  0%| F|  |TAMS 0x0000000793600000| PB 0x0000000793600000| Untracked 
| 311|0x0000000793700000, 0x0000000793700000, 0x0000000793800000|  0%| F|  |TAMS 0x0000000793700000| PB 0x0000000793700000| Untracked 
| 312|0x0000000793800000, 0x0000000793800000, 0x0000000793900000|  0%| F|  |TAMS 0x0000000793800000| PB 0x0000000793800000| Untracked 
| 313|0x0000000793900000, 0x0000000793900000, 0x0000000793a00000|  0%| F|  |TAMS 0x0000000793900000| PB 0x0000000793900000| Untracked 
| 314|0x0000000793a00000, 0x0000000793a00000, 0x0000000793b00000|  0%| F|  |TAMS 0x0000000793a00000| PB 0x0000000793a00000| Untracked 
| 315|0x0000000793b00000, 0x0000000793b00000, 0x0000000793c00000|  0%| F|  |TAMS 0x0000000793b00000| PB 0x0000000793b00000| Untracked 
| 316|0x0000000793c00000, 0x0000000793c00000, 0x0000000793d00000|  0%| F|  |TAMS 0x0000000793c00000| PB 0x0000000793c00000| Untracked 
| 317|0x0000000793d00000, 0x0000000793d00000, 0x0000000793e00000|  0%| F|  |TAMS 0x0000000793d00000| PB 0x0000000793d00000| Untracked 
| 318|0x0000000793e00000, 0x0000000793e00000, 0x0000000793f00000|  0%| F|  |TAMS 0x0000000793e00000| PB 0x0000000793e00000| Untracked 
| 319|0x0000000793f00000, 0x0000000793f00000, 0x0000000794000000|  0%| F|  |TAMS 0x0000000793f00000| PB 0x0000000793f00000| Untracked 
| 320|0x0000000794000000, 0x0000000794000000, 0x0000000794100000|  0%| F|  |TAMS 0x0000000794000000| PB 0x0000000794000000| Untracked 
| 321|0x0000000794100000, 0x0000000794100000, 0x0000000794200000|  0%| F|  |TAMS 0x0000000794100000| PB 0x0000000794100000| Untracked 
| 322|0x0000000794200000, 0x0000000794200000, 0x0000000794300000|  0%| F|  |TAMS 0x0000000794200000| PB 0x0000000794200000| Untracked 
| 323|0x0000000794300000, 0x0000000794300000, 0x0000000794400000|  0%| F|  |TAMS 0x0000000794300000| PB 0x0000000794300000| Untracked 
| 324|0x0000000794400000, 0x0000000794400000, 0x0000000794500000|  0%| F|  |TAMS 0x0000000794400000| PB 0x0000000794400000| Untracked 
| 325|0x0000000794500000, 0x0000000794500000, 0x0000000794600000|  0%| F|  |TAMS 0x0000000794500000| PB 0x0000000794500000| Untracked 
| 326|0x0000000794600000, 0x0000000794600000, 0x0000000794700000|  0%| F|  |TAMS 0x0000000794600000| PB 0x0000000794600000| Untracked 
| 327|0x0000000794700000, 0x0000000794700000, 0x0000000794800000|  0%| F|  |TAMS 0x0000000794700000| PB 0x0000000794700000| Untracked 
| 328|0x0000000794800000, 0x0000000794800000, 0x0000000794900000|  0%| F|  |TAMS 0x0000000794800000| PB 0x0000000794800000| Untracked 
| 329|0x0000000794900000, 0x0000000794900000, 0x0000000794a00000|  0%| F|  |TAMS 0x0000000794900000| PB 0x0000000794900000| Untracked 
| 330|0x0000000794a00000, 0x0000000794a00000, 0x0000000794b00000|  0%| F|  |TAMS 0x0000000794a00000| PB 0x0000000794a00000| Untracked 
| 331|0x0000000794b00000, 0x0000000794b00000, 0x0000000794c00000|  0%| F|  |TAMS 0x0000000794b00000| PB 0x0000000794b00000| Untracked 
| 332|0x0000000794c00000, 0x0000000794c00000, 0x0000000794d00000|  0%| F|  |TAMS 0x0000000794c00000| PB 0x0000000794c00000| Untracked 
| 333|0x0000000794d00000, 0x0000000794d00000, 0x0000000794e00000|  0%| F|  |TAMS 0x0000000794d00000| PB 0x0000000794d00000| Untracked 
| 334|0x0000000794e00000, 0x0000000794e00000, 0x0000000794f00000|  0%| F|  |TAMS 0x0000000794e00000| PB 0x0000000794e00000| Untracked 
| 335|0x0000000794f00000, 0x0000000794f00000, 0x0000000795000000|  0%| F|  |TAMS 0x0000000794f00000| PB 0x0000000794f00000| Untracked 
| 336|0x0000000795000000, 0x0000000795000000, 0x0000000795100000|  0%| F|  |TAMS 0x0000000795000000| PB 0x0000000795000000| Untracked 
| 337|0x0000000795100000, 0x0000000795100000, 0x0000000795200000|  0%| F|  |TAMS 0x0000000795100000| PB 0x0000000795100000| Untracked 
| 338|0x0000000795200000, 0x0000000795200000, 0x0000000795300000|  0%| F|  |TAMS 0x0000000795200000| PB 0x0000000795200000| Untracked 
| 339|0x0000000795300000, 0x0000000795300000, 0x0000000795400000|  0%| F|  |TAMS 0x0000000795300000| PB 0x0000000795300000| Untracked 
| 340|0x0000000795400000, 0x0000000795400000, 0x0000000795500000|  0%| F|  |TAMS 0x0000000795400000| PB 0x0000000795400000| Untracked 
| 341|0x0000000795500000, 0x0000000795600000, 0x0000000795600000|100%| E|CS|TAMS 0x0000000795500000| PB 0x0000000795500000| Complete 
| 342|0x0000000795600000, 0x0000000795700000, 0x0000000795700000|100%| E|CS|TAMS 0x0000000795600000| PB 0x0000000795600000| Complete 
| 343|0x0000000795700000, 0x0000000795800000, 0x0000000795800000|100%| E|CS|TAMS 0x0000000795700000| PB 0x0000000795700000| Complete 
| 344|0x0000000795800000, 0x0000000795900000, 0x0000000795900000|100%| E|CS|TAMS 0x0000000795800000| PB 0x0000000795800000| Complete 
| 345|0x0000000795900000, 0x0000000795a00000, 0x0000000795a00000|100%| E|CS|TAMS 0x0000000795900000| PB 0x0000000795900000| Complete 
| 346|0x0000000795a00000, 0x0000000795b00000, 0x0000000795b00000|100%| E|CS|TAMS 0x0000000795a00000| PB 0x0000000795a00000| Complete 
| 347|0x0000000795b00000, 0x0000000795c00000, 0x0000000795c00000|100%| E|CS|TAMS 0x0000000795b00000| PB 0x0000000795b00000| Complete 
| 348|0x0000000795c00000, 0x0000000795d00000, 0x0000000795d00000|100%| E|CS|TAMS 0x0000000795c00000| PB 0x0000000795c00000| Complete 
| 349|0x0000000795d00000, 0x0000000795e00000, 0x0000000795e00000|100%| E|CS|TAMS 0x0000000795d00000| PB 0x0000000795d00000| Complete 
| 350|0x0000000795e00000, 0x0000000795f00000, 0x0000000795f00000|100%| E|CS|TAMS 0x0000000795e00000| PB 0x0000000795e00000| Complete 
| 351|0x0000000795f00000, 0x0000000796000000, 0x0000000796000000|100%| E|CS|TAMS 0x0000000795f00000| PB 0x0000000795f00000| Complete 
| 352|0x0000000796000000, 0x0000000796100000, 0x0000000796100000|100%| E|CS|TAMS 0x0000000796000000| PB 0x0000000796000000| Complete 
| 353|0x0000000796100000, 0x0000000796200000, 0x0000000796200000|100%| E|CS|TAMS 0x0000000796100000| PB 0x0000000796100000| Complete 
| 354|0x0000000796200000, 0x0000000796300000, 0x0000000796300000|100%| E|CS|TAMS 0x0000000796200000| PB 0x0000000796200000| Complete 
| 355|0x0000000796300000, 0x0000000796400000, 0x0000000796400000|100%| E|CS|TAMS 0x0000000796300000| PB 0x0000000796300000| Complete 
| 356|0x0000000796400000, 0x0000000796500000, 0x0000000796500000|100%| E|CS|TAMS 0x0000000796400000| PB 0x0000000796400000| Complete 
| 357|0x0000000796500000, 0x0000000796600000, 0x0000000796600000|100%| E|CS|TAMS 0x0000000796500000| PB 0x0000000796500000| Complete 
| 358|0x0000000796600000, 0x0000000796700000, 0x0000000796700000|100%| E|CS|TAMS 0x0000000796600000| PB 0x0000000796600000| Complete 
| 359|0x0000000796700000, 0x0000000796800000, 0x0000000796800000|100%| E|CS|TAMS 0x0000000796700000| PB 0x0000000796700000| Complete 
| 360|0x0000000796800000, 0x0000000796900000, 0x0000000796900000|100%| E|CS|TAMS 0x0000000796800000| PB 0x0000000796800000| Complete 
| 361|0x0000000796900000, 0x0000000796a00000, 0x0000000796a00000|100%| E|CS|TAMS 0x0000000796900000| PB 0x0000000796900000| Complete 
| 362|0x0000000796a00000, 0x0000000796b00000, 0x0000000796b00000|100%| E|CS|TAMS 0x0000000796a00000| PB 0x0000000796a00000| Complete 
| 363|0x0000000796b00000, 0x0000000796c00000, 0x0000000796c00000|100%| E|CS|TAMS 0x0000000796b00000| PB 0x0000000796b00000| Complete 
| 364|0x0000000796c00000, 0x0000000796d00000, 0x0000000796d00000|100%| E|CS|TAMS 0x0000000796c00000| PB 0x0000000796c00000| Complete 
| 365|0x0000000796d00000, 0x0000000796e00000, 0x0000000796e00000|100%| E|CS|TAMS 0x0000000796d00000| PB 0x0000000796d00000| Complete 
| 366|0x0000000796e00000, 0x0000000796f00000, 0x0000000796f00000|100%| E|CS|TAMS 0x0000000796e00000| PB 0x0000000796e00000| Complete 
| 367|0x0000000796f00000, 0x0000000797000000, 0x0000000797000000|100%| E|CS|TAMS 0x0000000796f00000| PB 0x0000000796f00000| Complete 
| 368|0x0000000797000000, 0x0000000797100000, 0x0000000797100000|100%| E|CS|TAMS 0x0000000797000000| PB 0x0000000797000000| Complete 
| 369|0x0000000797100000, 0x0000000797200000, 0x0000000797200000|100%| E|CS|TAMS 0x0000000797100000| PB 0x0000000797100000| Complete 
| 370|0x0000000797200000, 0x0000000797200000, 0x0000000797300000|  0%| F|  |TAMS 0x0000000797200000| PB 0x0000000797200000| Untracked 
| 371|0x0000000797300000, 0x0000000797300000, 0x0000000797400000|  0%| F|  |TAMS 0x0000000797300000| PB 0x0000000797300000| Untracked 
| 372|0x0000000797400000, 0x0000000797400000, 0x0000000797500000|  0%| F|  |TAMS 0x0000000797400000| PB 0x0000000797400000| Untracked 
| 373|0x0000000797500000, 0x0000000797500000, 0x0000000797600000|  0%| F|  |TAMS 0x0000000797500000| PB 0x0000000797500000| Untracked 
| 374|0x0000000797600000, 0x0000000797600000, 0x0000000797700000|  0%| F|  |TAMS 0x0000000797600000| PB 0x0000000797600000| Untracked 
| 375|0x0000000797700000, 0x0000000797700000, 0x0000000797800000|  0%| F|  |TAMS 0x0000000797700000| PB 0x0000000797700000| Untracked 
| 376|0x0000000797800000, 0x0000000797800000, 0x0000000797900000|  0%| F|  |TAMS 0x0000000797800000| PB 0x0000000797800000| Untracked 
| 377|0x0000000797900000, 0x0000000797900000, 0x0000000797a00000|  0%| F|  |TAMS 0x0000000797900000| PB 0x0000000797900000| Untracked 
| 378|0x0000000797a00000, 0x0000000797a00000, 0x0000000797b00000|  0%| F|  |TAMS 0x0000000797a00000| PB 0x0000000797a00000| Untracked 
| 379|0x0000000797b00000, 0x0000000797b00000, 0x0000000797c00000|  0%| F|  |TAMS 0x0000000797b00000| PB 0x0000000797b00000| Untracked 
| 380|0x0000000797c00000, 0x0000000797c00000, 0x0000000797d00000|  0%| F|  |TAMS 0x0000000797c00000| PB 0x0000000797c00000| Untracked 
| 381|0x0000000797d00000, 0x0000000797d00000, 0x0000000797e00000|  0%| F|  |TAMS 0x0000000797d00000| PB 0x0000000797d00000| Untracked 
| 382|0x0000000797e00000, 0x0000000797e00000, 0x0000000797f00000|  0%| F|  |TAMS 0x0000000797e00000| PB 0x0000000797e00000| Untracked 
| 383|0x0000000797f00000, 0x0000000797f00000, 0x0000000798000000|  0%| F|  |TAMS 0x0000000797f00000| PB 0x0000000797f00000| Untracked 
| 384|0x0000000798000000, 0x0000000798000000, 0x0000000798100000|  0%| F|  |TAMS 0x0000000798000000| PB 0x0000000798000000| Untracked 
| 385|0x0000000798100000, 0x0000000798100000, 0x0000000798200000|  0%| F|  |TAMS 0x0000000798100000| PB 0x0000000798100000| Untracked 
| 386|0x0000000798200000, 0x0000000798200000, 0x0000000798300000|  0%| F|  |TAMS 0x0000000798200000| PB 0x0000000798200000| Untracked 
| 387|0x0000000798300000, 0x0000000798300000, 0x0000000798400000|  0%| F|  |TAMS 0x0000000798300000| PB 0x0000000798300000| Untracked 
| 388|0x0000000798400000, 0x0000000798400000, 0x0000000798500000|  0%| F|  |TAMS 0x0000000798400000| PB 0x0000000798400000| Untracked 
| 389|0x0000000798500000, 0x0000000798500000, 0x0000000798600000|  0%| F|  |TAMS 0x0000000798500000| PB 0x0000000798500000| Untracked 
| 390|0x0000000798600000, 0x0000000798600000, 0x0000000798700000|  0%| F|  |TAMS 0x0000000798600000| PB 0x0000000798600000| Untracked 
| 391|0x0000000798700000, 0x0000000798700000, 0x0000000798800000|  0%| F|  |TAMS 0x0000000798700000| PB 0x0000000798700000| Untracked 
| 392|0x0000000798800000, 0x0000000798800000, 0x0000000798900000|  0%| F|  |TAMS 0x0000000798800000| PB 0x0000000798800000| Untracked 
| 393|0x0000000798900000, 0x0000000798900000, 0x0000000798a00000|  0%| F|  |TAMS 0x0000000798900000| PB 0x0000000798900000| Untracked 
| 394|0x0000000798a00000, 0x0000000798a00000, 0x0000000798b00000|  0%| F|  |TAMS 0x0000000798a00000| PB 0x0000000798a00000| Untracked 
| 395|0x0000000798b00000, 0x0000000798b00000, 0x0000000798c00000|  0%| F|  |TAMS 0x0000000798b00000| PB 0x0000000798b00000| Untracked 
| 396|0x0000000798c00000, 0x0000000798c00000, 0x0000000798d00000|  0%| F|  |TAMS 0x0000000798c00000| PB 0x0000000798c00000| Untracked 
| 397|0x0000000798d00000, 0x0000000798d00000, 0x0000000798e00000|  0%| F|  |TAMS 0x0000000798d00000| PB 0x0000000798d00000| Untracked 
| 398|0x0000000798e00000, 0x0000000798e00000, 0x0000000798f00000|  0%| F|  |TAMS 0x0000000798e00000| PB 0x0000000798e00000| Untracked 
| 399|0x0000000798f00000, 0x0000000798f00000, 0x0000000799000000|  0%| F|  |TAMS 0x0000000798f00000| PB 0x0000000798f00000| Untracked 
| 400|0x0000000799000000, 0x0000000799000000, 0x0000000799100000|  0%| F|  |TAMS 0x0000000799000000| PB 0x0000000799000000| Untracked 
| 401|0x0000000799100000, 0x0000000799100000, 0x0000000799200000|  0%| F|  |TAMS 0x0000000799100000| PB 0x0000000799100000| Untracked 
| 402|0x0000000799200000, 0x0000000799200000, 0x0000000799300000|  0%| F|  |TAMS 0x0000000799200000| PB 0x0000000799200000| Untracked 
| 403|0x0000000799300000, 0x0000000799300000, 0x0000000799400000|  0%| F|  |TAMS 0x0000000799300000| PB 0x0000000799300000| Untracked 
| 404|0x0000000799400000, 0x0000000799400000, 0x0000000799500000|  0%| F|  |TAMS 0x0000000799400000| PB 0x0000000799400000| Untracked 
| 405|0x0000000799500000, 0x0000000799500000, 0x0000000799600000|  0%| F|  |TAMS 0x0000000799500000| PB 0x0000000799500000| Untracked 
| 406|0x0000000799600000, 0x0000000799600000, 0x0000000799700000|  0%| F|  |TAMS 0x0000000799600000| PB 0x0000000799600000| Untracked 
| 407|0x0000000799700000, 0x0000000799700000, 0x0000000799800000|  0%| F|  |TAMS 0x0000000799700000| PB 0x0000000799700000| Untracked 
| 408|0x0000000799800000, 0x0000000799800000, 0x0000000799900000|  0%| F|  |TAMS 0x0000000799800000| PB 0x0000000799800000| Untracked 
| 409|0x0000000799900000, 0x0000000799900000, 0x0000000799a00000|  0%| F|  |TAMS 0x0000000799900000| PB 0x0000000799900000| Untracked 
| 410|0x0000000799a00000, 0x0000000799a00000, 0x0000000799b00000|  0%| F|  |TAMS 0x0000000799a00000| PB 0x0000000799a00000| Untracked 
| 411|0x0000000799b00000, 0x0000000799b00000, 0x0000000799c00000|  0%| F|  |TAMS 0x0000000799b00000| PB 0x0000000799b00000| Untracked 
| 412|0x0000000799c00000, 0x0000000799c00000, 0x0000000799d00000|  0%| F|  |TAMS 0x0000000799c00000| PB 0x0000000799c00000| Untracked 
| 413|0x0000000799d00000, 0x0000000799d00000, 0x0000000799e00000|  0%| F|  |TAMS 0x0000000799d00000| PB 0x0000000799d00000| Untracked 
| 414|0x0000000799e00000, 0x0000000799e00000, 0x0000000799f00000|  0%| F|  |TAMS 0x0000000799e00000| PB 0x0000000799e00000| Untracked 
| 415|0x0000000799f00000, 0x0000000799f00000, 0x000000079a000000|  0%| F|  |TAMS 0x0000000799f00000| PB 0x0000000799f00000| Untracked 
| 416|0x000000079a000000, 0x000000079a000000, 0x000000079a100000|  0%| F|  |TAMS 0x000000079a000000| PB 0x000000079a000000| Untracked 
| 417|0x000000079a100000, 0x000000079a100000, 0x000000079a200000|  0%| F|  |TAMS 0x000000079a100000| PB 0x000000079a100000| Untracked 
| 418|0x000000079a200000, 0x000000079a200000, 0x000000079a300000|  0%| F|  |TAMS 0x000000079a200000| PB 0x000000079a200000| Untracked 
| 419|0x000000079a300000, 0x000000079a300000, 0x000000079a400000|  0%| F|  |TAMS 0x000000079a300000| PB 0x000000079a300000| Untracked 
| 420|0x000000079a400000, 0x000000079a400000, 0x000000079a500000|  0%| F|  |TAMS 0x000000079a400000| PB 0x000000079a400000| Untracked 
| 421|0x000000079a500000, 0x000000079a500000, 0x000000079a600000|  0%| F|  |TAMS 0x000000079a500000| PB 0x000000079a500000| Untracked 
| 422|0x000000079a600000, 0x000000079a600000, 0x000000079a700000|  0%| F|  |TAMS 0x000000079a600000| PB 0x000000079a600000| Untracked 
| 423|0x000000079a700000, 0x000000079a700000, 0x000000079a800000|  0%| F|  |TAMS 0x000000079a700000| PB 0x000000079a700000| Untracked 
| 424|0x000000079a800000, 0x000000079a800000, 0x000000079a900000|  0%| F|  |TAMS 0x000000079a800000| PB 0x000000079a800000| Untracked 
| 425|0x000000079a900000, 0x000000079a900000, 0x000000079aa00000|  0%| F|  |TAMS 0x000000079a900000| PB 0x000000079a900000| Untracked 
| 426|0x000000079aa00000, 0x000000079aa00000, 0x000000079ab00000|  0%| F|  |TAMS 0x000000079aa00000| PB 0x000000079aa00000| Untracked 
| 427|0x000000079ab00000, 0x000000079ab00000, 0x000000079ac00000|  0%| F|  |TAMS 0x000000079ab00000| PB 0x000000079ab00000| Untracked 
| 428|0x000000079ac00000, 0x000000079ac00000, 0x000000079ad00000|  0%| F|  |TAMS 0x000000079ac00000| PB 0x000000079ac00000| Untracked 
| 429|0x000000079ad00000, 0x000000079ad00000, 0x000000079ae00000|  0%| F|  |TAMS 0x000000079ad00000| PB 0x000000079ad00000| Untracked 
| 430|0x000000079ae00000, 0x000000079ae00000, 0x000000079af00000|  0%| F|  |TAMS 0x000000079ae00000| PB 0x000000079ae00000| Untracked 
| 431|0x000000079af00000, 0x000000079af00000, 0x000000079b000000|  0%| F|  |TAMS 0x000000079af00000| PB 0x000000079af00000| Untracked 
| 432|0x000000079b000000, 0x000000079b069100, 0x000000079b100000| 41%| E|  |TAMS 0x000000079b000000| PB 0x000000079b000000| Complete 
| 433|0x000000079b100000, 0x000000079b200000, 0x000000079b200000|100%| E|CS|TAMS 0x000000079b100000| PB 0x000000079b100000| Complete 
| 434|0x000000079b200000, 0x000000079b300000, 0x000000079b300000|100%| E|CS|TAMS 0x000000079b200000| PB 0x000000079b200000| Complete 
| 440|0x000000079b800000, 0x000000079b900000, 0x000000079b900000|100%| E|CS|TAMS 0x000000079b800000| PB 0x000000079b800000| Complete 
| 441|0x000000079b900000, 0x000000079ba00000, 0x000000079ba00000|100%| E|CS|TAMS 0x000000079b900000| PB 0x000000079b900000| Complete 
| 442|0x000000079ba00000, 0x000000079bb00000, 0x000000079bb00000|100%| E|CS|TAMS 0x000000079ba00000| PB 0x000000079ba00000| Complete 
| 443|0x000000079bb00000, 0x000000079bc00000, 0x000000079bc00000|100%| E|CS|TAMS 0x000000079bb00000| PB 0x000000079bb00000| Complete 
| 444|0x000000079bc00000, 0x000000079bd00000, 0x000000079bd00000|100%| E|CS|TAMS 0x000000079bc00000| PB 0x000000079bc00000| Complete 
| 445|0x000000079bd00000, 0x000000079be00000, 0x000000079be00000|100%| E|CS|TAMS 0x000000079bd00000| PB 0x000000079bd00000| Complete 
| 446|0x000000079be00000, 0x000000079bf00000, 0x000000079bf00000|100%| E|CS|TAMS 0x000000079be00000| PB 0x000000079be00000| Complete 
| 447|0x000000079bf00000, 0x000000079c000000, 0x000000079c000000|100%| E|CS|TAMS 0x000000079bf00000| PB 0x000000079bf00000| Complete 
| 448|0x000000079c000000, 0x000000079c100000, 0x000000079c100000|100%| E|CS|TAMS 0x000000079c000000| PB 0x000000079c000000| Complete 
| 449|0x000000079c100000, 0x000000079c200000, 0x000000079c200000|100%| E|CS|TAMS 0x000000079c100000| PB 0x000000079c100000| Complete 
| 450|0x000000079c200000, 0x000000079c300000, 0x000000079c300000|100%| E|CS|TAMS 0x000000079c200000| PB 0x000000079c200000| Complete 
| 451|0x000000079c300000, 0x000000079c400000, 0x000000079c400000|100%| E|CS|TAMS 0x000000079c300000| PB 0x000000079c300000| Complete 
| 452|0x000000079c400000, 0x000000079c500000, 0x000000079c500000|100%| E|CS|TAMS 0x000000079c400000| PB 0x000000079c400000| Complete 
| 453|0x000000079c500000, 0x000000079c600000, 0x000000079c600000|100%| E|CS|TAMS 0x000000079c500000| PB 0x000000079c500000| Complete 
| 454|0x000000079c600000, 0x000000079c700000, 0x000000079c700000|100%| E|CS|TAMS 0x000000079c600000| PB 0x000000079c600000| Complete 
| 482|0x000000079e200000, 0x000000079e300000, 0x000000079e300000|100%| E|CS|TAMS 0x000000079e200000| PB 0x000000079e200000| Complete 
| 483|0x000000079e300000, 0x000000079e400000, 0x000000079e400000|100%| E|CS|TAMS 0x000000079e300000| PB 0x000000079e300000| Complete 
| 484|0x000000079e400000, 0x000000079e500000, 0x000000079e500000|100%| E|CS|TAMS 0x000000079e400000| PB 0x000000079e400000| Complete 
| 485|0x000000079e500000, 0x000000079e600000, 0x000000079e600000|100%| E|CS|TAMS 0x000000079e500000| PB 0x000000079e500000| Complete 
| 486|0x000000079e600000, 0x000000079e700000, 0x000000079e700000|100%| E|CS|TAMS 0x000000079e600000| PB 0x000000079e600000| Complete 
| 487|0x000000079e700000, 0x000000079e800000, 0x000000079e800000|100%| E|CS|TAMS 0x000000079e700000| PB 0x000000079e700000| Complete 
| 488|0x000000079e800000, 0x000000079e900000, 0x000000079e900000|100%| E|CS|TAMS 0x000000079e800000| PB 0x000000079e800000| Complete 
| 489|0x000000079e900000, 0x000000079ea00000, 0x000000079ea00000|100%| E|CS|TAMS 0x000000079e900000| PB 0x000000079e900000| Complete 
| 490|0x000000079ea00000, 0x000000079eb00000, 0x000000079eb00000|100%| E|CS|TAMS 0x000000079ea00000| PB 0x000000079ea00000| Complete 
| 491|0x000000079eb00000, 0x000000079ec00000, 0x000000079ec00000|100%| E|CS|TAMS 0x000000079eb00000| PB 0x000000079eb00000| Complete 
|2024|0x00000007fe800000, 0x00000007fe900000, 0x00000007fe900000|100%| E|CS|TAMS 0x00000007fe800000| PB 0x00000007fe800000| Complete 
|2025|0x00000007fe900000, 0x00000007fea00000, 0x00000007fea00000|100%| O|  |TAMS 0x00000007fe900000| PB 0x00000007fe900000| Untracked 
|2026|0x00000007fea00000, 0x00000007feb00000, 0x00000007feb00000|100%| O|  |TAMS 0x00000007fea00000| PB 0x00000007fea00000| Untracked 
|2027|0x00000007feb00000, 0x00000007fec00000, 0x00000007fec00000|100%| O|  |TAMS 0x00000007feb00000| PB 0x00000007feb00000| Untracked 
|2028|0x00000007fec00000, 0x00000007fed00000, 0x00000007fed00000|100%| O|  |TAMS 0x00000007fec00000| PB 0x00000007fec00000| Untracked 
|2029|0x00000007fed00000, 0x00000007fee00000, 0x00000007fee00000|100%| O|  |TAMS 0x00000007fed00000| PB 0x00000007fed00000| Untracked 
|2030|0x00000007fee00000, 0x00000007fef00000, 0x00000007fef00000|100%| O|  |TAMS 0x00000007fee00000| PB 0x00000007fee00000| Untracked 
|2031|0x00000007fef00000, 0x00000007ff000000, 0x00000007ff000000|100%| O|  |TAMS 0x00000007fef00000| PB 0x00000007fef00000| Untracked 
|2032|0x00000007ff000000, 0x00000007ff100000, 0x00000007ff100000|100%| E|CS|TAMS 0x00000007ff000000| PB 0x00000007ff000000| Complete 
|2033|0x00000007ff100000, 0x00000007ff200000, 0x00000007ff200000|100%| O|  |TAMS 0x00000007ff100000| PB 0x00000007ff100000| Untracked 
|2034|0x00000007ff200000, 0x00000007ff300000, 0x00000007ff300000|100%| O|  |TAMS 0x00000007ff200000| PB 0x00000007ff200000| Untracked 
|2035|0x00000007ff300000, 0x00000007ff400000, 0x00000007ff400000|100%| O|  |TAMS 0x00000007ff300000| PB 0x00000007ff300000| Untracked 
|2036|0x00000007ff400000, 0x00000007ff500000, 0x00000007ff500000|100%| O|  |TAMS 0x00000007ff400000| PB 0x00000007ff400000| Untracked 
|2037|0x00000007ff500000, 0x00000007ff600000, 0x00000007ff600000|100%| E|CS|TAMS 0x00000007ff500000| PB 0x00000007ff500000| Complete 
|2038|0x00000007ff600000, 0x00000007ff700000, 0x00000007ff700000|100%| O|  |TAMS 0x00000007ff600000| PB 0x00000007ff600000| Untracked 
|2039|0x00000007ff700000, 0x00000007ff800000, 0x00000007ff800000|100%| O|  |TAMS 0x00000007ff700000| PB 0x00000007ff700000| Untracked 
|2040|0x00000007ff800000, 0x00000007ff900000, 0x00000007ff900000|100%| O|  |TAMS 0x00000007ff800000| PB 0x00000007ff800000| Untracked 
|2041|0x00000007ff900000, 0x00000007ffa00000, 0x00000007ffa00000|100%| O|  |TAMS 0x00000007ff900000| PB 0x00000007ff900000| Untracked 
|2042|0x00000007ffa00000, 0x00000007ffb00000, 0x00000007ffb00000|100%| O|  |TAMS 0x00000007ffa00000| PB 0x00000007ffa00000| Untracked 
|2043|0x00000007ffb00000, 0x00000007ffc00000, 0x00000007ffc00000|100%| O|  |TAMS 0x00000007ffb00000| PB 0x00000007ffb00000| Untracked 
|2044|0x00000007ffc00000, 0x00000007ffd00000, 0x00000007ffd00000|100%| O|  |TAMS 0x00000007ffc00000| PB 0x00000007ffc00000| Untracked 
|2045|0x00000007ffd00000, 0x00000007ffe00000, 0x00000007ffe00000|100%| O|  |TAMS 0x00000007ffd00000| PB 0x00000007ffd00000| Untracked 
|2046|0x00000007ffe00000, 0x00000007fff00000, 0x00000007fff00000|100%| O|  |TAMS 0x00000007ffe00000| PB 0x00000007ffe00000| Untracked 
|2047|0x00000007fff00000, 0x0000000800000000, 0x0000000800000000|100%| E|CS|TAMS 0x00000007fff00000| PB 0x00000007fff00000| Complete 

Card table byte_map: [0x0000000105392000,0x0000000105792000] _byte_map_base: 0x0000000101792000

Marking Bits: (CMBitMap*) 0x00007f80d3813810
 Bits: [0x000000012b0c0000, 0x000000012d0c0000)

Polling page: 0x0000000104abf000

Metaspace:

Usage:
  Non-class:    254.14 MB used.
      Class:     36.18 MB used.
       Both:    290.32 MB used.

Virtual space:
  Non-class space:      320.00 MB reserved,     256.50 MB ( 80%) committed,  5 nodes.
      Class space:        1.00 GB reserved,      37.94 MB (  4%) committed,  1 nodes.
             Both:        1.31 GB reserved,     294.44 MB ( 22%) committed. 

Chunk freelists:
   Non-Class:  14.23 MB
       Class:  10.01 MB
        Both:  24.24 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 490.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 317.
num_arena_births: 5308.
num_arena_deaths: 318.
num_vsnodes_births: 6.
num_vsnodes_deaths: 0.
num_space_committed: 4707.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 782.
num_chunks_taken_from_freelist: 20743.
num_chunk_merges: 437.
num_chunk_splits: 12225.
num_chunks_enlarged: 5966.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=259300Kb used=8892Kb max_used=9387Kb free=250407Kb
 bounds [0x000000011b387000, 0x000000011bcb7000, 0x000000012b0c0000]
CodeHeap 'profiled nmethods': size=259296Kb used=47075Kb max_used=51355Kb free=212220Kb
 bounds [0x000000010b0c0000, 0x000000010e360000, 0x000000011adf8000]
CodeHeap 'non-nmethods': size=5692Kb used=3078Kb max_used=3145Kb free=2613Kb
 bounds [0x000000011adf8000, 0x000000011b128000, 0x000000011b387000]
 total_blobs=28461 nmethods=26576 adapters=1786
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 51.909 Thread 0x00007f80d4066200 33190       4       kotlin.sequences.FilteringSequence$iterator$1::calcNext (68 bytes)
Event: 51.911 Thread 0x00007f80d4066200 nmethod 33190 0x000000011b98a490 code [0x000000011b98a640, 0x000000011b98a918]
Event: 51.911 Thread 0x00007f80d4066200 33191       4       kotlinx.coroutines.debug.internal.ConcurrentWeakMap::remove (53 bytes)
Event: 51.912 Thread 0x00007f80d4066200 nmethod 33191 0x000000011b85f810 code [0x000000011b85f9c0, 0x000000011b85fb50]
Event: 51.912 Thread 0x00007f80d4066200 33207       4       com.intellij.openapi.editor.ex.util.LexerEditorHighlighter::getAttributes (60 bytes)
Event: 51.924 Thread 0x00007f80d4066200 nmethod 33207 0x000000011b9cae10 code [0x000000011b9cb080, 0x000000011b9cbdf0]
Event: 51.924 Thread 0x00007f80d4066200 33196   !   4       com.intellij.openapi.editor.ex.util.LexerEditorHighlighter$HighlighterIteratorImpl::getTokenType (41 bytes)
Event: 51.926 Thread 0x00007f80d4066200 nmethod 33196 0x000000011b80fd90 code [0x000000011b80ff40, 0x000000011b8101d0]
Event: 51.926 Thread 0x00007f80d4066200 33197       4       com.intellij.openapi.editor.ex.util.SegmentArrayWithData::getSegmentData (50 bytes)
Event: 51.926 Thread 0x00007f80d4066200 nmethod 33197 0x000000011b6a2590 code [0x000000011b6a2720, 0x000000011b6a2880]
Event: 51.926 Thread 0x00007f80d4066200 33192       4       com.intellij.openapi.application.impl.ApplicationImpl::isWriteActionPending (9 bytes)
Event: 51.927 Thread 0x00007f80d4066200 nmethod 33192 0x000000011b4a1e10 code [0x000000011b4a1f80, 0x000000011b4a2028]
Event: 51.927 Thread 0x00007f80d4066200 33195       4       sun.java2d.marlin.MarlinTileGenerator::nextTile (72 bytes)
Event: 51.927 Thread 0x00007f80d4066200 nmethod 33195 0x000000011b8d3310 code [0x000000011b8d34a0, 0x000000011b8d35c0]
Event: 51.927 Thread 0x00007f80d4066200 33199       4       java.awt.image.ColorModel::getRGBdefault (4 bytes)
Event: 51.927 Thread 0x00007f80d4066200 nmethod 33199 0x000000011b4afb10 code [0x000000011b4afc80, 0x000000011b4afcf0]
Event: 51.946 Thread 0x00007f80d4066200 33209       4       kotlinx.coroutines.debug.internal.DebugProbesImpl$CoroutineOwner::getCallerFrame (17 bytes)
Event: 51.946 Thread 0x00007f80d4066200 nmethod 33209 0x000000011b4b8590 code [0x000000011b4b8720, 0x000000011b4b87e8]
Event: 52.167 Thread 0x00007f80d4066a00 33210       3       java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject::canReacquire (38 bytes)
Event: 52.168 Thread 0x00007f80d4066a00 nmethod 33210 0x000000010b306e10 code [0x000000010b306fc0, 0x000000010b307318]

GC Heap History (20 events):
Event: 11.734 GC heap before
{Heap before GC invocations=23 (full 0):
 garbage-first heap   total 411648K, used 350468K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 157 young (160768K), 21 survivors (21504K)
 Metaspace       used 232482K, committed 235840K, reserved 1310720K
  class space    used 28690K, committed 30144K, reserved 1048576K
}
Event: 11.755 GC heap after
{Heap after GC invocations=24 (full 0):
 garbage-first heap   total 411648K, used 229862K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 16 young (16384K), 16 survivors (16384K)
 Metaspace       used 232482K, committed 235840K, reserved 1310720K
  class space    used 28690K, committed 30144K, reserved 1048576K
}
Event: 12.518 GC heap before
{Heap before GC invocations=25 (full 0):
 garbage-first heap   total 411648K, used 369126K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 145 young (148480K), 16 survivors (16384K)
 Metaspace       used 235664K, committed 239168K, reserved 1310720K
  class space    used 29080K, committed 30592K, reserved 1048576K
}
Event: 12.534 GC heap after
{Heap after GC invocations=26 (full 0):
 garbage-first heap   total 411648K, used 237045K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 235664K, committed 239168K, reserved 1310720K
  class space    used 29080K, committed 30592K, reserved 1048576K
}
Event: 13.559 GC heap before
{Heap before GC invocations=26 (full 0):
 garbage-first heap   total 411648K, used 374261K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 140 young (143360K), 14 survivors (14336K)
 Metaspace       used 244745K, committed 248320K, reserved 1310720K
  class space    used 30190K, committed 31744K, reserved 1048576K
}
Event: 13.579 GC heap after
{Heap after GC invocations=27 (full 0):
 garbage-first heap   total 411648K, used 230869K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 244745K, committed 248320K, reserved 1310720K
  class space    used 30190K, committed 31744K, reserved 1048576K
}
Event: 44.027 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 411648K, used 366037K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 142 young (145408K), 9 survivors (9216K)
 Metaspace       used 250260K, committed 253888K, reserved 1310720K
  class space    used 30770K, committed 32384K, reserved 1048576K
}
Event: 44.039 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total 411648K, used 235707K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 250260K, committed 253888K, reserved 1310720K
  class space    used 30770K, committed 32384K, reserved 1048576K
}
Event: 47.775 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 411648K, used 369851K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 147 young (150528K), 13 survivors (13312K)
 Metaspace       used 281512K, committed 285568K, reserved 1310720K
  class space    used 35018K, committed 36800K, reserved 1048576K
}
Event: 47.790 GC heap after
{Heap after GC invocations=30 (full 0):
 garbage-first heap   total 411648K, used 246663K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 19 young (19456K), 19 survivors (19456K)
 Metaspace       used 281512K, committed 285568K, reserved 1310720K
  class space    used 35018K, committed 36800K, reserved 1048576K
}
Event: 48.717 GC heap before
{Heap before GC invocations=30 (full 0):
 garbage-first heap   total 411648K, used 372615K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 140 young (143360K), 19 survivors (19456K)
 Metaspace       used 288771K, committed 292928K, reserved 1310720K
  class space    used 36088K, committed 37888K, reserved 1048576K
}
Event: 48.733 GC heap after
{Heap after GC invocations=31 (full 0):
 garbage-first heap   total 411648K, used 240946K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 288771K, committed 292928K, reserved 1310720K
  class space    used 36088K, committed 37888K, reserved 1048576K
}
Event: 49.368 GC heap before
{Heap before GC invocations=31 (full 0):
 garbage-first heap   total 411648K, used 370994K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 140 young (143360K), 12 survivors (12288K)
 Metaspace       used 294743K, committed 298944K, reserved 1310720K
  class space    used 36826K, committed 38656K, reserved 1048576K
}
Event: 49.380 GC heap after
{Heap after GC invocations=32 (full 0):
 garbage-first heap   total 411648K, used 256649K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 17 young (17408K), 17 survivors (17408K)
 Metaspace       used 294743K, committed 298944K, reserved 1310720K
  class space    used 36826K, committed 38656K, reserved 1048576K
}
Event: 49.986 GC heap before
{Heap before GC invocations=33 (full 0):
 garbage-first heap   total 429056K, used 375433K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 129 young (132096K), 17 survivors (17408K)
 Metaspace       used 295728K, committed 299840K, reserved 1376256K
  class space    used 36897K, committed 38720K, reserved 1048576K
}
Event: 50.002 GC heap after
{Heap after GC invocations=34 (full 0):
 garbage-first heap   total 429056K, used 275340K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 17 young (17408K), 17 survivors (17408K)
 Metaspace       used 295728K, committed 299840K, reserved 1376256K
  class space    used 36897K, committed 38720K, reserved 1048576K
}
Event: 50.422 GC heap before
{Heap before GC invocations=34 (full 0):
 garbage-first heap   total 429056K, used 387980K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 128 young (131072K), 17 survivors (17408K)
 Metaspace       used 295959K, committed 300096K, reserved 1376256K
  class space    used 36921K, committed 38720K, reserved 1048576K
}
Event: 50.443 GC heap after
{Heap after GC invocations=35 (full 0):
 garbage-first heap   total 429056K, used 281600K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 295959K, committed 300096K, reserved 1376256K
  class space    used 36921K, committed 38720K, reserved 1048576K
}
Event: 51.062 GC heap before
{Heap before GC invocations=35 (full 0):
 garbage-first heap   total 429056K, used 391167K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 122 young (124928K), 14 survivors (14336K)
 Metaspace       used 296577K, committed 300736K, reserved 1376256K
  class space    used 36971K, committed 38784K, reserved 1048576K
}
Event: 51.076 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total 429056K, used 294707K [0x0000000780000000, 0x0000000800000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 296577K, committed 300736K, reserved 1376256K
  class space    used 36971K, committed 38784K, reserved 1048576K
}

Dll operation events (17 events):
Event: 0.008 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libjava.dylib
Event: 0.111 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libzip.dylib
Event: 0.120 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libnio.dylib
Event: 0.128 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libzip.dylib
Event: 0.161 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libnet.dylib
Event: 0.221 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libjimage.dylib
Event: 0.335 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libverify.dylib
Event: 0.395 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libextnet.dylib
Event: 0.430 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libprefs.dylib
Event: 0.463 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libawt.dylib
Event: 0.493 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libawt_lwawt.dylib
Event: 0.513 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libfontmanager.dylib
Event: 0.545 Loaded shared library /Applications/WebStorm.app/Contents/lib/jna/amd64/libjnidispatch.jnilib
Event: 0.603 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libmanagement.dylib
Event: 0.608 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libmanagement_ext.dylib
Event: 1.079 Loaded shared library /Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libosxui.dylib
Event: 1.496 Loaded shared library /Applications/WebStorm.app/Contents/bin/libmacscreenmenu64.dylib

Deoptimization events (20 events):
Event: 51.227 Thread 0x00007f80d1ced000 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000000011b5f5d2c relative=0x000000000000146c
Event: 51.227 Thread 0x00007f80d1ced000 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000000011b5f5d2c method=java.lang.String.join(Ljava/lang/CharSequence;Ljava/lang/Iterable;)Ljava/lang/String; @ 27 c2
Event: 51.227 Thread 0x00007f80d1ced000 DEOPT PACKING pc=0x000000011b5f5d2c sp=0x00007000111aefb0
Event: 51.227 Thread 0x00007f80d1ced000 DEOPT UNPACKING pc=0x000000011ae4bf99 sp=0x00007000111aef98 mode 2
Event: 51.228 Thread 0x00007f80d1ced000 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000000011b5f5d2c relative=0x000000000000146c
Event: 51.228 Thread 0x00007f80d1ced000 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000000011b5f5d2c method=java.lang.String.join(Ljava/lang/CharSequence;Ljava/lang/Iterable;)Ljava/lang/String; @ 27 c2
Event: 51.228 Thread 0x00007f80d1ced000 DEOPT PACKING pc=0x000000011b5f5d2c sp=0x00007000111aef60
Event: 51.228 Thread 0x00007f80d1ced000 DEOPT UNPACKING pc=0x000000011ae4bf99 sp=0x00007000111aef48 mode 2
Event: 51.240 Thread 0x00007f80d1ced000 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000000011b5f5d2c relative=0x000000000000146c
Event: 51.240 Thread 0x00007f80d1ced000 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000000011b5f5d2c method=java.lang.String.join(Ljava/lang/CharSequence;Ljava/lang/Iterable;)Ljava/lang/String; @ 27 c2
Event: 51.240 Thread 0x00007f80d1ced000 DEOPT PACKING pc=0x000000011b5f5d2c sp=0x00007000111aef60
Event: 51.240 Thread 0x00007f80d1ced000 DEOPT UNPACKING pc=0x000000011ae4bf99 sp=0x00007000111aef48 mode 2
Event: 51.240 Thread 0x00007f80d1ced000 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000000011b5f5d2c relative=0x000000000000146c
Event: 51.240 Thread 0x00007f80d1ced000 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000000011b5f5d2c method=java.lang.String.join(Ljava/lang/CharSequence;Ljava/lang/Iterable;)Ljava/lang/String; @ 27 c2
Event: 51.240 Thread 0x00007f80d1ced000 DEOPT PACKING pc=0x000000011b5f5d2c sp=0x00007000111aef60
Event: 51.240 Thread 0x00007f80d1ced000 DEOPT UNPACKING pc=0x000000011ae4bf99 sp=0x00007000111aef48 mode 2
Event: 51.876 Thread 0x00007f80d39a1e00 Uncommon trap: trap_request=0xfffffff4 fr.pc=0x000000011bb7fda8 relative=0x0000000000001728
Event: 51.876 Thread 0x00007f80d39a1e00 Uncommon trap: reason=null_check action=make_not_entrant pc=0x000000011bb7fda8 method=sun.font.CStrike$GlyphInfoCache.get(I)J @ 65 c2
Event: 51.877 Thread 0x00007f80d39a1e00 DEOPT PACKING pc=0x000000011bb7fda8 sp=0x000070000d064970
Event: 51.877 Thread 0x00007f80d39a1e00 DEOPT UNPACKING pc=0x000000011ae4bf99 sp=0x000070000d064880 mode 2

Classes loaded (20 events):
Event: 49.585 Loading class sun/reflect/generics/tree/FloatSignature
Event: 49.585 Loading class sun/reflect/generics/tree/FloatSignature done
Event: 49.789 Loading class java/util/regex/Pattern$CIBackRef
Event: 49.789 Loading class java/util/regex/Pattern$CIBackRef done
Event: 49.884 Loading class sun/net/www/protocol/jar/JarURLConnection
Event: 49.885 Loading class sun/net/www/protocol/jar/JarURLConnection done
Event: 49.885 Loading class sun/net/www/protocol/jar/JarFileFactory
Event: 49.885 Loading class sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
Event: 49.885 Loading class sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController done
Event: 49.885 Loading class sun/net/www/protocol/jar/JarFileFactory done
Event: 49.885 Loading class sun/net/www/protocol/jar/URLJarFile
Event: 49.885 Loading class sun/net/www/protocol/jar/URLJarFile done
Event: 49.893 Loading class sun/net/www/protocol/jar/URLJarFile$URLJarFileEntry
Event: 49.893 Loading class sun/net/www/protocol/jar/URLJarFile$URLJarFileEntry done
Event: 49.893 Loading class sun/net/www/protocol/jar/JarURLConnection$JarURLInputStream
Event: 49.893 Loading class sun/net/www/protocol/jar/JarURLConnection$JarURLInputStream done
Event: 50.349 Loading class java/lang/CharacterData01
Event: 50.349 Loading class java/lang/CharacterData01 done
Event: 51.301 Loading class java/util/PriorityQueue$Itr
Event: 51.301 Loading class java/util/PriorityQueue$Itr done

Classes unloaded (20 events):
Event: 8.517 Thread 0x00007f80d2005b40 Unloading class 0x0000000131a7b000 'java/lang/invoke/LambdaForm$MH+0x0000000131a7b000'
Event: 8.517 Thread 0x00007f80d2005b40 Unloading class 0x0000000131a7a800 'java/lang/invoke/LambdaForm$DMH+0x0000000131a7a800'
Event: 8.517 Thread 0x00007f80d2005b40 Unloading class 0x0000000131a7a400 'java/lang/invoke/LambdaForm$DMH+0x0000000131a7a400'
Event: 8.517 Thread 0x00007f80d2005b40 Unloading class 0x0000000131a79c00 'java/lang/invoke/LambdaForm$DMH+0x0000000131a79c00'
Event: 8.517 Thread 0x00007f80d2005b40 Unloading class 0x0000000131a79800 'java/lang/invoke/LambdaForm$DMH+0x0000000131a79800'
Event: 8.517 Thread 0x00007f80d2005b40 Unloading class 0x0000000131a79400 'java/lang/invoke/LambdaForm$DMH+0x0000000131a79400'
Event: 8.517 Thread 0x00007f80d2005b40 Unloading class 0x0000000131a78c00 'java/lang/invoke/LambdaForm$DMH+0x0000000131a78c00'
Event: 8.517 Thread 0x00007f80d2005b40 Unloading class 0x0000000131a78800 'java/lang/invoke/LambdaForm$DMH+0x0000000131a78800'
Event: 8.517 Thread 0x00007f80d2005b40 Unloading class 0x0000000131a78400 'java/lang/invoke/LambdaForm$DMH+0x0000000131a78400'
Event: 44.171 Thread 0x00007f80d2005b40 Unloading class 0x0000000132cddc00 'java/lang/invoke/LambdaForm$MH+0x0000000132cddc00'
Event: 44.171 Thread 0x00007f80d2005b40 Unloading class 0x0000000132cdd000 'java/lang/invoke/LambdaForm$DMH+0x0000000132cdd000'
Event: 44.171 Thread 0x00007f80d2005b40 Unloading class 0x0000000132cdcc00 'java/lang/invoke/LambdaForm$DMH+0x0000000132cdcc00'
Event: 44.171 Thread 0x00007f80d2005b40 Unloading class 0x0000000132cdc400 'java/lang/invoke/LambdaForm$DMH+0x0000000132cdc400'
Event: 51.262 Thread 0x00007f80d2005b40 Unloading class 0x0000000133514c00 'java/lang/invoke/LambdaForm$DMH+0x0000000133514c00'
Event: 51.262 Thread 0x00007f80d2005b40 Unloading class 0x0000000133514800 'java/lang/invoke/LambdaForm$DMH+0x0000000133514800'
Event: 51.262 Thread 0x00007f80d2005b40 Unloading class 0x0000000133514000 'java/lang/invoke/LambdaForm$DMH+0x0000000133514000'
Event: 51.262 Thread 0x00007f80d2005b40 Unloading class 0x0000000133481c00 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor93'
Event: 51.262 Thread 0x00007f80d2005b40 Unloading class 0x0000000133481400 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor91'
Event: 51.262 Thread 0x00007f80d2005b40 Unloading class 0x0000000133480c00 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor89'
Event: 51.262 Thread 0x00007f80d2005b40 Unloading class 0x0000000133480000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor86'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 49.311 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f597eb0}> (0x000000078f597eb0) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.311 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f5989c0}> (0x000000078f5989c0) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f599468}> (0x000000078f599468) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f599f10}> (0x000000078f599f10) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f59a9b8}> (0x000000078f59a9b8) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f59b4c8}> (0x000000078f59b4c8) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f59bfd8}> (0x000000078f59bfd8) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f59ce00}> (0x000000078f59ce00) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f59d910}> (0x000000078f59d910) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f59e420}> (0x000000078f59e420) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f59ef30}> (0x000000078f59ef30) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f59fa40}> (0x000000078f59fa40) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f5a0550}> (0x000000078f5a0550) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f5a1060}> (0x000000078f5a1060) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.312 Thread 0x00007f80d71cfc00 Exception <a 'sun/nio/fs/UnixException'{0x000000078f5a1c28}> (0x000000078f5a1c28) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 49.802 Thread 0x00007f80d749cc00 Exception <a 'java/lang/NoSuchMethodError'{0x0000000794df9a98}: 'void com.intellij.packageChecker.service.ProjectDependenciesModelState.<init>(com.intellij.openapi.project.Project)'> (0x0000000794df9a98) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 784]
Event: 49.802 Thread 0x00007f80d749cc00 Exception <a 'java/lang/NoSuchMethodError'{0x0000000794dfb098}: 'void com.intellij.packageChecker.service.ProjectDependenciesModelState.<init>(com.intellij.openapi.project.Project, kotlinx.coroutines.CoroutineScope)'> (0x0000000794dfb098) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 784]
Event: 49.802 Thread 0x00007f80d749cc00 Exception <a 'java/lang/NoSuchMethodError'{0x0000000794dfc6b8}: 'void com.intellij.packageChecker.service.ProjectDependenciesModelState.<init>(kotlinx.coroutines.CoroutineScope)'> (0x0000000794dfc6b8) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 784]
Event: 51.231 Thread 0x00007f80d1ced000 Exception <a 'java/lang/NoSuchMethodError'{0x00000007957a4fd8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007957a4fd8) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 784]
Event: 51.876 Thread 0x00007f80d39a1e00 Implicit null exception at 0x000000011bb7e857 to 0x000000011bb7fd74

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 49.688 Executing VM operation: ICBufferFull
Event: 49.688 Executing VM operation: ICBufferFull done
Event: 49.888 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 49.889 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 49.970 Executing VM operation: ICBufferFull
Event: 49.970 Executing VM operation: ICBufferFull done
Event: 49.986 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 50.002 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 50.422 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 50.443 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 50.893 Executing VM operation: ICBufferFull
Event: 50.893 Executing VM operation: ICBufferFull done
Event: 51.062 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 51.076 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 51.261 Executing VM operation: G1PauseRemark
Event: 51.285 Executing VM operation: G1PauseRemark done
Event: 51.390 Executing VM operation: G1PauseCleanup
Event: 51.390 Executing VM operation: G1PauseCleanup done
Event: 51.882 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 51.882 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (20 events):
Event: 28.504 Protecting memory [0x000070000da8c000,0x000070000da90000] with protection modes 3
Event: 37.079 Protecting memory [0x000070000db8f000,0x000070000db93000] with protection modes 3
Event: 42.773 Protecting memory [0x000070000d57a000,0x000070000d57e000] with protection modes 0
Event: 42.773 Protecting memory [0x000070000d57a000,0x000070000d57e000] with protection modes 3
Event: 42.940 Protecting memory [0x000070000d57a000,0x000070000d57e000] with protection modes 0
Event: 43.035 Protecting memory [0x000070000db0f000,0x000070000db13000] with protection modes 0
Event: 43.035 Protecting memory [0x000070000db0f000,0x000070000db13000] with protection modes 3
Event: 43.086 Protecting memory [0x000070000e42a000,0x000070000e42e000] with protection modes 0
Event: 43.166 Protecting memory [0x000070000fc75000,0x000070000fc79000] with protection modes 0
Event: 43.166 Protecting memory [0x000070000fc75000,0x000070000fc79000] with protection modes 3
Event: 43.418 Protecting memory [0x000070000fc75000,0x000070000fc79000] with protection modes 0
Event: 43.418 Protecting memory [0x000070000fc75000,0x000070000fc79000] with protection modes 3
Event: 44.236 Protecting memory [0x000070000db0f000,0x000070000db13000] with protection modes 0
Event: 44.238 Protecting memory [0x000070000fc75000,0x000070000fc79000] with protection modes 0
Event: 44.239 Protecting memory [0x000070000fd78000,0x000070000fd7c000] with protection modes 0
Event: 44.239 Protecting memory [0x00007000134c7000,0x00007000134cb000] with protection modes 0
Event: 44.241 Protecting memory [0x00007000135ca000,0x00007000135ce000] with protection modes 0
Event: 44.243 Protecting memory [0x00007000136cd000,0x00007000136d1000] with protection modes 0
Event: 48.811 Protecting memory [0x00007000137d0000,0x00007000137d4000] with protection modes 0
Event: 49.012 Protecting memory [0x00007000138d3000,0x00007000138d7000] with protection modes 0

Nmethod flushes (20 events):
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d4e2b90
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d4e3e10
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d53f510
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d573a90
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d574090
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d5c0610
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d8a6e10
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d8aaf90
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d8ae110
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d8ae910
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d926c90
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d9cf690
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d9d0210
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d9d0810
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010d9d2d10
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010da4cf90
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010dc00510
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010dc04010
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010e045090
Event: 51.281 Thread 0x00007f80d2005b40 flushing  nmethod 0x000000010e0d9890

Events (20 events):
Event: 28.504 Thread 0x00007f80d41da200 Thread exited: 0x00007f80d41da200
Event: 37.079 Thread 0x00007f80d41cf600 Thread exited: 0x00007f80d41cf600
Event: 42.773 Thread 0x00007f80d1ceb800 Thread added: 0x00007f80d452e800
Event: 42.773 Thread 0x00007f80d452e800 Thread exited: 0x00007f80d452e800
Event: 42.940 Thread 0x00007f80d1ceb800 Thread added: 0x00007f80d2bfe600
Event: 43.034 Thread 0x00007f80d1ceb800 Thread added: 0x00007f80d3b01c00
Event: 43.035 Thread 0x00007f80d3b01c00 Thread exited: 0x00007f80d3b01c00
Event: 43.086 Thread 0x00007f80d39a1e00 Thread added: 0x00007f80d1906800
Event: 43.166 Thread 0x00007f80d1ceb800 Thread added: 0x00007f80d70ba800
Event: 43.166 Thread 0x00007f80d70ba800 Thread exited: 0x00007f80d70ba800
Event: 43.418 Thread 0x00007f80d1ceb800 Thread added: 0x00007f80d18b2a00
Event: 43.418 Thread 0x00007f80d18b2a00 Thread exited: 0x00007f80d18b2a00
Event: 44.236 Thread 0x00007f80d1906800 Thread added: 0x00007f80d4bee800
Event: 44.238 Thread 0x00007f80d4bee800 Thread added: 0x00007f80d61a9400
Event: 44.238 Thread 0x00007f80d4bee800 Thread added: 0x00007f80d3c9ee00
Event: 44.239 Thread 0x00007f80d61a9400 Thread added: 0x00007f80d6116a00
Event: 44.240 Thread 0x00007f80d3c9ee00 Thread added: 0x00007f80d7407600
Event: 44.243 Thread 0x00007f80d6116a00 Thread added: 0x00007f80d18e9800
Event: 48.811 Thread 0x00007f80d749cc00 Thread added: 0x00007f80d38db600
Event: 49.012 Thread 0x00007f80d749cc00 Thread added: 0x00007f80d4093000


Dynamic libraries:
0x00007ff82a44b000 	/usr/lib/libiconv.2.dylib
0x00007ff82a3a3000 	/usr/lib/libcharset.1.dylib
0x00007ff82a40f000 	/usr/lib/libSystem.B.dylib
0x00007ff82a409000 	/usr/lib/system/libcache.dylib
0x00007ff82a3c1000 	/usr/lib/system/libcommonCrypto.dylib
0x00007ff82a3eb000 	/usr/lib/system/libcompiler_rt.dylib
0x00007ff82a3df000 	/usr/lib/system/libcopyfile.dylib
0x00007ff81c00e000 	/usr/lib/system/libcorecrypto.dylib
0x00007ff81c106000 	/usr/lib/system/libdispatch.dylib
0x00007ff81c2b7000 	/usr/lib/system/libdyld.dylib
0x00007ff82a3ff000 	/usr/lib/system/libkeymgr.dylib
0x00007ff82a3a4000 	/usr/lib/system/libmacho.dylib
0x00007ff8296ff000 	/usr/lib/system/libquarantine.dylib
0x00007ff82a3fc000 	/usr/lib/system/libremovefile.dylib
0x00007ff821d7a000 	/usr/lib/system/libsystem_asl.dylib
0x00007ff81bfa8000 	/usr/lib/system/libsystem_blocks.dylib
0x00007ff81c151000 	/usr/lib/system/libsystem_c.dylib
0x00007ff82a3f3000 	/usr/lib/system/libsystem_collections.dylib
0x00007ff828642000 	/usr/lib/system/libsystem_configuration.dylib
0x00007ff8273ef000 	/usr/lib/system/libsystem_containermanager.dylib
0x00007ff829f7d000 	/usr/lib/system/libsystem_coreservices.dylib
0x00007ff81f811000 	/usr/lib/system/libsystem_darwin.dylib
0x00007ffd29b7c000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x00007ff82a400000 	/usr/lib/system/libsystem_dnssd.dylib
0x00007ffd29b80000 	/usr/lib/system/libsystem_eligibility.dylib
0x00007ff81c14e000 	/usr/lib/system/libsystem_featureflags.dylib
0x00007ff81c2ee000 	/usr/lib/system/libsystem_info.dylib
0x00007ff82a335000 	/usr/lib/system/libsystem_m.dylib
0x00007ff81c0c2000 	/usr/lib/system/libsystem_malloc.dylib
0x00007ff821ceb000 	/usr/lib/system/libsystem_networkextension.dylib
0x00007ff81fc4d000 	/usr/lib/system/libsystem_notify.dylib
0x00007ff828646000 	/usr/lib/system/libsystem_sandbox.dylib
0x00007ffd29b87000 	/usr/lib/system/libsystem_sanitizers.dylib
0x00007ff82a3f8000 	/usr/lib/system/libsystem_secinit.dylib
0x00007ff81c26f000 	/usr/lib/system/libsystem_kernel.dylib
0x00007ff81c2e4000 	/usr/lib/system/libsystem_platform.dylib
0x00007ff81c2ab000 	/usr/lib/system/libsystem_pthread.dylib
0x00007ff823ab4000 	/usr/lib/system/libsystem_symptoms.dylib
0x00007ff81bff3000 	/usr/lib/system/libsystem_trace.dylib
0x00007ff82a3ce000 	/usr/lib/system/libunwind.dylib
0x00007ff81bfac000 	/usr/lib/system/libxpc.dylib
0x00007ff81bed9000 	/usr/lib/libobjc.A.dylib
0x00007ff81c31c000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x00007ff82d9dc000 	/usr/lib/swift/libswiftCore.dylib
0x00007ff81c257000 	/usr/lib/libc++abi.dylib
0x00007ff82a3d7000 	/usr/lib/liboah.dylib
0x00007ff81c1da000 	/usr/lib/libc++.1.dylib
0x00007ff81d373000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x00007ff836690000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x00007ff81fb06000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x00007ff82a411000 	/usr/lib/libfakelink.dylib
0x00007ff81f572000 	/usr/lib/libicucore.A.dylib
0x00007ff82a414000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00007ff831e67000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x00007ff81f1a3000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x00007ff81fb87000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x00007ff835cdd000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x00007ff923720000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x00007ff821947000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x00007ff8259af000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x00007ff81f81c000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x00007ff824bdd000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x00007ff829f84000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x00007ff82a4ed000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x00007ff823a34000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x00007ff81c7ba000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x00007ff82ba47000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x00007ff8259bd000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x00007ff81d001000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x00007ff82a572000 	/usr/lib/libapple_nghttp2.dylib
0x00007ff82a5a9000 	/usr/lib/libcompression.dylib
0x00007ff823692000 	/usr/lib/libsqlite3.dylib
0x00007ff82a321000 	/usr/lib/libz.1.dylib
0x00007ff824b50000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x00007ff822280000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x00007ff82e5f6000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x00007ff8239d0000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x00007ff821d05000 	/usr/lib/libenergytrace.dylib
0x00007ff823abc000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x00007ff824b7f000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x00007ff829724000 	/usr/lib/libbsm.0.dylib
0x00007ff82a3a8000 	/usr/lib/system/libkxld.dylib
0x00007ffd28a52000 	/usr/lib/libCoreEntitlements.dylib
0x00007ffc291b0000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x00007ff823679000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x00007ff821d06000 	/usr/lib/libMobileGestalt.dylib
0x00007ff829f63000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x00007ff82970c000 	/usr/lib/libcoretls.dylib
0x00007ff82bab4000 	/usr/lib/libcoretls_cfhelpers.dylib
0x00007ff82a5a3000 	/usr/lib/libpam.2.dylib
0x00007ff82bb2e000 	/usr/lib/libxar.1.dylib
0x00007ff8259e8000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x00007ff824c64000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x00007ff82a464000 	/usr/lib/libarchive.2.dylib
0x00007ff825a30000 	/usr/lib/libxml2.2.dylib
0x00007ff828653000 	/usr/lib/liblangid.dylib
0x00007ff82fd17000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x00007ffb31464000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x00007ffc30450000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x00007ffc30fc0000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x00007ff92622c000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00007ff923bdf000 	/usr/lib/swift/libswiftDarwin.dylib
0x00007ff833be2000 	/usr/lib/swift/libswiftDispatch.dylib
0x00007ff92627d000 	/usr/lib/swift/libswiftIOKit.dylib
0x00007ffd297b2000 	/usr/lib/swift/libswiftSystem.dylib
0x00007ff92623d000 	/usr/lib/swift/libswiftXPC.dylib
0x00007ffd2982a000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x00007ffd2982d000 	/usr/lib/swift/libswift_Concurrency.dylib
0x00007ffd29973000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x00007ffd29a0e000 	/usr/lib/swift/libswift_errno.dylib
0x00007ffd29a10000 	/usr/lib/swift/libswift_math.dylib
0x00007ffd29a13000 	/usr/lib/swift/libswift_signal.dylib
0x00007ffd29a14000 	/usr/lib/swift/libswift_stdio.dylib
0x00007ffd29a15000 	/usr/lib/swift/libswift_time.dylib
0x00007ff836694000 	/usr/lib/swift/libswiftos.dylib
0x00007ffd29a16000 	/usr/lib/swift/libswiftsys_time.dylib
0x00007ffd29a17000 	/usr/lib/swift/libswiftunistd.dylib
0x00007ff82864e000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x00007ff926129000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x00007ff825ba1000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x00007ff821874000 	/usr/lib/libboringssl.dylib
0x00007ff823aa4000 	/usr/lib/libdns_services.dylib
0x00007ff92537b000 	/usr/lib/libquic.dylib
0x00007ff82d964000 	/usr/lib/libusrtcp.dylib
0x00007ffa28a01000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x00007ffd296eb000 	/usr/lib/swift/libswiftDistributed.dylib
0x00007ff821946000 	/usr/lib/libnetwork.dylib
0x00007ff82a550000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x00007ffd2971b000 	/usr/lib/swift/libswiftObservation.dylib
0x00007ff81ce91000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x00007ff82963e000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x00007ff828764000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x00007ff829421000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00007ff82741c000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x00007ff8238ed000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x00007ff833f26000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x00007ff829631000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x00007ff827ddb000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00007ff821db1000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x00007ff82782d000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x00007ff832112000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x00007ff821d91000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x00007ff839584000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x00007ff82c679000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x00007ff81e356000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x00007ff8276dc000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x00007ff827433000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x00007ff82743c000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x00007ff825b10000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x00007ff82a5a1000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x00007ff82c67b000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x00007ff825309000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x00007ffc2fa3d000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x00007ff82c6bf000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x00007ff932d34000 	/usr/lib/swift/libswiftMetal.dylib
0x00007ffa210df000 	/usr/lib/swift/libswiftOSLog.dylib
0x00007ff93772a000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00007ff93bc84000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00007ff929b92000 	/usr/lib/swift/libswiftsimd.dylib
0x00007ff829617000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x00007ff82864d000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x00007ff82bab6000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x00007ffb2e0eb000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x00007ff82bb3c000 	/usr/lib/libutil.dylib
0x00007ff82ba95000 	/usr/lib/liblzma.5.dylib
0x00007ff824c6c000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x00007ff831e3e000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x00007ff82bb71000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x00007ff81cc2d000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x00007ff822b5b000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x00007ff81e834000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00007ffb37c20000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x00007ff82138e000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x00007ff82c1cd000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x00007ff8256ad000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x00007ff82c21d000 	/System/Library/PrivateFrameworks/FontServices.framework/libhvf.dylib
0x00007ffc2c21a000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x00007ffd29736000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x00007ffd298cb000 	/usr/lib/swift/libswift_RegexParser.dylib
0x00007ff82c53d000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x00007ff82bfef000 	/usr/lib/libexpat.1.dylib
0x00007ff82cc8a000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x00007ff82ccb7000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x00007ff82cdac000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x00007ff82c589000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x00007ff82bce6000 	/usr/lib/libate.dylib
0x00007ff82cd4c000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x00007ff82cd43000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x00007ffc20207000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x00007ffb37ce6000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x00007ffb1f499000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x00007ffb388fb000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x00007ffb37c21000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x00007ff82816d000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x00007ffa24796000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x00007ffc2edc7000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x00007ffb1f4a5000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x00007ffb20a83000 	/System/Library/Frameworks/OpenCL.framework/Versions/A/OpenCL
0x00007ff82c1be000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00007ffb1f4f6000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00007ffb1f4b8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00007ffb1f6b2000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00007ffb1f4c1000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00007ffb1f4b5000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00007ffd28b81000 	/usr/lib/libRosetta.dylib
0x00007ffb1f4a0000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x00007ff828587000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x00007ff829ebd000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x00007ff8297a9000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x00007ff829d02000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x00007ff829ab9000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x00007ff829d41000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00007ffb25881000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00007ffb25864000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x00007ff81caa6000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00007ff92b09e000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00007ff937b30000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00007ff92620b000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x00007ff83437f000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00007ff9261ab000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x00007ff82bd86000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x00007ff834340000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x00007ff936dab000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x00007ff82e5e7000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x00007ff82e259000 	/usr/lib/libresolv.9.dylib
0x00007ff82c230000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x00007ff8365ec000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x00007ff82c00a000 	/usr/lib/libheimdal-asn1.dylib
0x00007ff825987000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x00007ff82e644000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x00007ff825993000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x00007ff82dfab000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x00007ff81ea58000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x00007ff82e19e000 	/usr/lib/libAudioStatistics.dylib
0x00007ff8276b8000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x00007ff81e481000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x00007ff8389de000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x00007ff82e145000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x00007ff82c0f4000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x00007ff82c212000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x00007ff82e5ca000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00007ff925465000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x00007ff82e46a000 	/usr/lib/libSMC.dylib
0x00007ff82cc55000 	/usr/lib/libAudioToolboxUtility.dylib
0x00007ff82e5d8000 	/usr/lib/libperfcheck.dylib
0x00007ffb2e6e8000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x00007ffa2f4e4000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x00007ffa287dc000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00007ff829f6e000 	/usr/lib/libbz2.1.0.dylib
0x00007ff9261da000 	/usr/lib/libmis.dylib
0x00007ff82df6b000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x00007ff82cdb2000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00007ff82c229000 	/usr/lib/libspindump.dylib
0x00007ffb23a1c000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x00007ff83448f000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x00007ff82bee0000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x00007ff832da6000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x00007ff82921e000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x00007ff82a68d000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x00007ff829793000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x00007ff82a58d000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x00007ff82a687000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x00007ff82876a000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x00007ff81cf8a000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x00007ffc2748c000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x00007ff82cd3c000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x00007ff82cd20000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x00007ff82cd46000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00007ffa28bdb000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x00007ff91f33a000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x00007ffd1f038000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x00007ff82bfa1000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x00007ff832e86000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x00007ff832de0000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x00007ff82e5ad000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x00007ff825b64000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x00007ffb2e329000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x00007ffd28bc6000 	/usr/lib/libTLE.dylib
0x00007ffa3703f000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x00007ff829734000 	/usr/lib/libmecab.dylib
0x00007ff81d087000 	/usr/lib/libCRFSuite.dylib
0x00007ff828655000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x00007ff829790000 	/usr/lib/libgermantok.dylib
0x00007ff82a548000 	/usr/lib/libThaiTokenizer.dylib
0x00007ff829702000 	/usr/lib/libCheckFix.dylib
0x00007ff824b81000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x00007ffc23c56000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x00007ff81fb47000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00007ff8259e5000 	/usr/lib/libapp_launch_measurement.dylib
0x00007ff937c32000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x00007ff82bb40000 	/usr/lib/libxslt.1.dylib
0x00007ff8296c8000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00007ff835e8b000 	/usr/lib/libcurl.4.dylib
0x00007ffd28f5a000 	/usr/lib/libcrypto.46.dylib
0x00007ffd294cb000 	/usr/lib/libssl.48.dylib
0x00007ff835b6f000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x00007ff835baa000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x00007ff82e272000 	/usr/lib/libsasl2.2.dylib
0x0000000104ad6000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libjli.dylib
0x00007ff838bd0000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x00007ff81fcb8000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x00007ffb3143d000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x00007ff831a2c000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x00007ff827d9e000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x00007ff82cf50000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x00007ffd1d667000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x00007ffb285db000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x00007ffd2212c000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x00007ffd2128f000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x00007ff827d8b000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x00007ff827aae000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x00007ff831a63000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x00007ff91e6ba000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x00007ff82597c000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00007ff831de1000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x00007ffb238fb000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x00007ffb29142000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00007ff923bde000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x00007ff83276f000 	/usr/lib/swift/libswiftFoundation.dylib
0x00007ffa36af6000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x00007ffa24e80000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x00007ffc310a1000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x00007ff831a53000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x00007ff82cef9000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x00007ffc23a07000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x00007ff833e55000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x00007ff835b16000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x00007ff8361fb000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x00007ff82e1e0000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x00007ff822d11000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x00007ff82cdc1000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x00007ff82e5bf000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x00007ff82e5b8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x00007ff82e1b6000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x00007ff8293ef000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x00007ff82cd7d000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x00007ff82e54a000 	/usr/lib/libcups.2.dylib
0x00007ffb22527000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00007ff92055e000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x00007ffc2628b000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x00007ff82faa3000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x00007ffc3b9e4000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x00007ffa1c30c000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x00007ff832cb6000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x00007ff8365ed000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x00007ff8294ac000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x00007ffb34f75000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x00007ff83215e000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00007ff92e311000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x00007ffd2888f000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x00007ff920603000 	/usr/lib/libAccessibility.dylib
0x00007ff833fce000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x00007ff8231e4000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x00007ff82e7b0000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x00007ffb23448000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00007ff82c05f000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x00007ff92627c000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00007ff82c013000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x00007ff8256f9000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x00007ff82ce35000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x00007ff827da3000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x00007ff93bc93000 	/usr/lib/swift/libswiftAccelerate.dylib
0x00007ff82bd7d000 	/usr/lib/libIOReport.dylib
0x00007ff82cf09000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00007ff929c3c000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x00007ff8211b3000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x0000000105df9000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/server/libjvm.dylib
0x0000000104aee000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libjimage.dylib
0x0000000104b70000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libjava.dylib
0x0000000105817000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libzip.dylib
0x0000000105847000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libnio.dylib
0x000000010582b000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libnet.dylib
0x0000000105ddf000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libverify.dylib
0x000000012fc43000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libextnet.dylib
0x000000012ff94000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libprefs.dylib
0x000000019b305000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libawt.dylib
0x000000019b3c5000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libmlib_image.dylib
0x00007ffb24b6c000 	/System/Library/Frameworks/JavaRuntimeSupport.framework/Versions/A/JavaRuntimeSupport
0x00007ff924ecd000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Carbon
0x00007ff83aeaa000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/CommonPanels.framework/Versions/A/CommonPanels
0x00007ff835e78000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Help.framework/Versions/A/Help
0x00007ff83aeae000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/ImageCapture.framework/Versions/A/ImageCapture
0x00007ff83ae8b000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/OpenScripting.framework/Versions/A/OpenScripting
0x00007ff83aea9000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Ink.framework/Versions/A/Ink
0x00007ff83aea6000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SecurityHI.framework/Versions/A/SecurityHI
0x000000019b6e2000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libawt_lwawt.dylib
0x000000019b54f000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libosxapp.dylib
0x00007ff923c3e000 	/System/Library/Frameworks/ExceptionHandling.framework/Versions/A/ExceptionHandling
0x00000001a9d6c000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libfontmanager.dylib
0x00000001aa12c000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libfreetype.dylib
0x00000001add1d000 	/Applications/WebStorm.app/Contents/lib/jna/amd64/libjnidispatch.jnilib
0x00000001b1f94000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libmanagement.dylib
0x00000001b4955000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libmanagement_ext.dylib
0x00007ff825865000 	/System/Library/PrivateFrameworks/ViewBridge.framework/Versions/A/ViewBridge
0x00007ffd28893000 	/usr/lib/libAccessibilityBaseImplementations.dylib
0x00007ffd22130000 	/System/Library/PrivateFrameworks/WritingToolsUI.framework/Versions/A/WritingToolsUI
0x00007ffc20ea0000 	/System/Library/PrivateFrameworks/GenerativeAssistantSettings.framework/Versions/A/GenerativeAssistantSettings
0x00007ffc39bae000 	/System/Library/PrivateFrameworks/TextComposer.framework/Versions/A/TextComposer
0x00007ff926233000 	/usr/lib/swift/libswiftIntents.dylib
0x00007ffd29741000 	/usr/lib/swift/libswiftSafariServices.dylib
0x00007ffd29746000 	/usr/lib/swift/libswiftSpatial.dylib
0x00007ffa2f3d7000 	/usr/lib/swift/libswiftWebKit.dylib
0x00007ffb22673000 	/System/Library/Frameworks/AppIntents.framework/Versions/A/AppIntents
0x00007ff92d0c9000 	/System/Library/Frameworks/SwiftUI.framework/Versions/A/SwiftUI
0x00007ffb29d1c000 	/System/Library/Frameworks/_AuthenticationServices_SwiftUI.framework/Versions/A/_AuthenticationServices_SwiftUI
0x00007ffb2c2bc000 	/System/Library/PrivateFrameworks/Anvil.framework/Versions/A/Anvil
0x00007ff92c761000 	/System/Library/PrivateFrameworks/AppStoreDaemon.framework/Versions/A/AppStoreDaemon
0x00007ff838c14000 	/System/Library/PrivateFrameworks/AppleMediaServices.framework/Versions/A/AppleMediaServices
0x00007ff9292f8000 	/System/Library/PrivateFrameworks/AppleMediaServicesUI.framework/Versions/A/AppleMediaServicesUI
0x00007ffc20e83000 	/System/Library/PrivateFrameworks/GenerativeAssistantCommon.framework/Versions/A/GenerativeAssistantCommon
0x00007ffa36224000 	/System/Library/PrivateFrameworks/JetEngine.framework/Versions/A/JetEngine
0x00007ffc26e5b000 	/System/Library/PrivateFrameworks/LinkMetadata.framework/Versions/A/LinkMetadata
0x00007ff926b7a000 	/System/Library/PrivateFrameworks/OnBoardingKit.framework/Versions/A/OnBoardingKit
0x00007ffc31df5000 	/System/Library/PrivateFrameworks/Settings.framework/Versions/A/Settings
0x00007ff82a1d2000 	/System/Library/Frameworks/CoreSpotlight.framework/Versions/A/CoreSpotlight
0x00007ffc270e1000 	/System/Library/PrivateFrameworks/LinkServices.framework/Versions/A/LinkServices
0x00007ffb359fd000 	/System/Library/PrivateFrameworks/DoNotDisturb.framework/Versions/A/DoNotDisturb
0x00007ff92b140000 	/System/Library/PrivateFrameworks/IntentsCore.framework/Versions/A/IntentsCore
0x00007ffb2e9be000 	/System/Library/PrivateFrameworks/AvailabilityKit.framework/Versions/A/AvailabilityKit
0x00007ffc389fa000 	/System/Library/PrivateFrameworks/StatusKit.framework/Versions/A/StatusKit
0x00007ffc303ea000 	/System/Library/PrivateFrameworks/RecapPerformanceTesting.framework/Versions/A/RecapPerformanceTesting
0x00007ffd28bc5000 	/usr/lib/libSpatial.dylib
0x00007ffb2e8a9000 	/System/Library/PrivateFrameworks/AuthenticationServicesCore.framework/Versions/A/AuthenticationServicesCore
0x00007ff92f70e000 	/System/Library/Frameworks/AuthenticationServices.framework/Versions/A/AuthenticationServices
0x00007ff82bebc000 	/System/Library/PrivateFrameworks/SharedWebCredentials.framework/Versions/A/SharedWebCredentials
0x00007ff9279e1000 	/System/Library/PrivateFrameworks/SafariCore.framework/Versions/A/SafariCore
0x00007ff92f236000 	/System/Library/PrivateFrameworks/SafariFoundation.framework/Versions/A/SafariFoundation
0x00007ffb2f998000 	/System/Library/PrivateFrameworks/CBORLibrary.framework/Versions/A/CBORLibrary
0x00007ffb3051f000 	/System/Library/PrivateFrameworks/Chirp.framework/Versions/A/Chirp
0x00007ffa37374000 	/System/Library/PrivateFrameworks/RemoteManagement.framework/Versions/A/RemoteManagement
0x00007ffc3069d000 	/System/Library/PrivateFrameworks/RemoteManagementModel.framework/Versions/A/RemoteManagementModel
0x00007ffc30723000 	/System/Library/PrivateFrameworks/RemoteManagementStore.framework/Versions/A/RemoteManagementStore
0x00007ff82fc7f000 	/usr/lib/libxcselect.dylib
0x00007ff92f1a3000 	/System/Library/PrivateFrameworks/DifferentialPrivacy.framework/Versions/A/DifferentialPrivacy
0x00007ff8329fb000 	/System/Library/Frameworks/Contacts.framework/Versions/A/Contacts
0x00007ffd1e029000 	/System/Library/PrivateFrameworks/VDAF.framework/Versions/A/VDAF
0x00007ff83282e000 	/System/Library/PrivateFrameworks/ContactsPersistence.framework/Versions/A/ContactsPersistence
0x00007ff831a69000 	/System/Library/PrivateFrameworks/AppleLDAP.framework/Versions/A/AppleLDAP
0x00007ff8361ff000 	/System/Library/Frameworks/ClassKit.framework/Versions/A/ClassKit
0x00007ff83834e000 	/System/Library/PrivateFrameworks/CoreSuggestions.framework/Versions/A/CoreSuggestions
0x00007ff832770000 	/System/Library/PrivateFrameworks/ContactsFoundation.framework/Versions/A/ContactsFoundation
0x00007ff833f95000 	/System/Library/PrivateFrameworks/vCard.framework/Versions/A/vCard
0x00007ffb31716000 	/System/Library/PrivateFrameworks/ContactsMetrics.framework/Versions/A/ContactsMetrics
0x00007ff82c2e7000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/login
0x00007ff825f6f000 	/System/Library/PrivateFrameworks/CloudDocs.framework/Versions/A/CloudDocs
0x00007ff8378d4000 	/System/Library/PrivateFrameworks/ConfigurationProfiles.framework/Versions/A/ConfigurationProfiles
0x00007ffb3511e000 	/System/Library/PrivateFrameworks/DMCUtilities.framework/Versions/A/DMCUtilities
0x00007ffc2740c000 	/System/Library/PrivateFrameworks/MDMClientLibrary.framework/Versions/A/MDMClientLibrary
0x00007ffc30718000 	/System/Library/PrivateFrameworks/RemoteManagementProtocol.framework/Versions/A/RemoteManagementProtocol
0x00007ffd28a67000 	/usr/lib/libEndpointSecuritySystem.dylib
0x00007ff832196000 	/System/Library/PrivateFrameworks/SystemAdministration.framework/Versions/A/SystemAdministration
0x00007ff91dc6a000 	/System/Library/PrivateFrameworks/PowerlogControl.framework/Versions/A/PowerlogControl
0x00007ff8296f5000 	/System/Library/Frameworks/ServiceManagement.framework/Versions/A/ServiceManagement
0x00007ff91dc6c000 	/usr/lib/libUniversalAccess.dylib
0x00007ff83958d000 	/System/Library/PrivateFrameworks/FamilyControls.framework/Versions/A/FamilyControls
0x00007ff831a21000 	/System/Library/Frameworks/DirectoryService.framework/Versions/A/DirectoryService
0x00007ff835f0e000 	/System/Library/PrivateFrameworks/LoginUIKit.framework/Versions/A/Frameworks/LoginUICore.framework/Versions/A/LoginUICore
0x00007ff82c4c5000 	/usr/lib/libodfde.dylib
0x00007ff839588000 	/System/Library/PrivateFrameworks/UniversalAccess.framework/Versions/A/Libraries/libUAPreferences.dylib
0x00007ff8395b0000 	/System/Library/PrivateFrameworks/CommerceKit.framework/Versions/A/Frameworks/CommerceCore.framework/Versions/A/CommerceCore
0x00007ffb350e3000 	/System/Library/PrivateFrameworks/DEPClientLibrary.framework/Versions/A/DEPClientLibrary
0x00007ff91dd47000 	/System/Library/PrivateFrameworks/SetupAssistantFramework.framework/Versions/A/SetupAssistantFramework
0x00007ff8364dd000 	/System/Library/PrivateFrameworks/AuthKitUI.framework/Versions/A/AuthKitUI
0x00007ff835f53000 	/System/Library/PrivateFrameworks/AppSSO.framework/Versions/A/AppSSO
0x00007ff92c4e0000 	/System/Library/PrivateFrameworks/LocalAuthenticationUI.framework/Versions/A/LocalAuthenticationUI
0x00007ffc2ee23000 	/System/Library/PrivateFrameworks/PlatformSSO.framework/Versions/A/PlatformSSO
0x00007ff92ceae000 	/System/Library/PrivateFrameworks/AOSUI.framework/Versions/A/AOSUI
0x00007ff91fd37000 	/System/Library/Frameworks/ContactsUI.framework/Versions/A/ContactsUI
0x00007ff832c1b000 	/System/Library/PrivateFrameworks/FamilyCircle.framework/Versions/A/FamilyCircle
0x00007ff939ae1000 	/System/Library/Frameworks/LinkPresentation.framework/Versions/A/LinkPresentation
0x00007ffc2eef2000 	/System/Library/PrivateFrameworks/PlatformSSOCore.framework/Versions/A/PlatformSSOCore
0x00007ff91fa5d000 	/System/Library/PrivateFrameworks/IASUtilities.framework/Versions/A/IASUtilities
0x00007ffc23966000 	/System/Library/PrivateFrameworks/IASUtilitiesCore.framework/Versions/A/IASUtilitiesCore
0x00007ff923ca2000 	/System/Library/Frameworks/WebKit.framework/Versions/A/WebKit
0x00007ff91bed9000 	/System/Library/Frameworks/JavaScriptCore.framework/Versions/A/JavaScriptCore
0x00007ffc27346000 	/System/Library/PrivateFrameworks/LocalAuthenticationCoreUI.framework/Versions/A/LocalAuthenticationCoreUI
0x00007ff925f98000 	/System/Library/Frameworks/LocalAuthentication.framework/Support/DaemonUtils.framework/Versions/A/DaemonUtils
0x00007ff9292c5000 	/System/Library/PrivateFrameworks/BridgeXPC.framework/Versions/A/BridgeXPC
0x00007ff82c307000 	/System/Library/PrivateFrameworks/CoreBrightness.framework/Versions/A/CoreBrightness
0x00007ff926c47000 	/System/Library/PrivateFrameworks/CoreKDL.framework/Versions/A/CoreKDL
0x00007ff92545e000 	/System/Library/PrivateFrameworks/EmbeddedOSSupportHost.framework/Versions/A/EmbeddedOSSupportHost
0x00007ffc3952e000 	/System/Library/PrivateFrameworks/SystemStatus.framework/Versions/A/SystemStatus
0x00007ff83861e000 	/System/Library/Frameworks/WebKit.framework/Versions/A/Frameworks/WebKitLegacy.framework/Versions/A/WebKitLegacy
0x00007ff836485000 	/System/Library/PrivateFrameworks/CorePrediction.framework/Versions/A/CorePrediction
0x00007ff82663f000 	/System/Library/Frameworks/WebKit.framework/Versions/A/Frameworks/WebCore.framework/Versions/A/Frameworks/libwebrtc.dylib
0x00007ff923c41000 	/System/Library/PrivateFrameworks/SafariSafeBrowsing.framework/Versions/A/SafariSafeBrowsing
0x00007ff82e2fe000 	/System/Library/Frameworks/SecurityInterface.framework/Versions/A/SecurityInterface
0x00007ff926b79000 	/System/Library/PrivateFrameworks/WebInspectorUI.framework/Versions/A/WebInspectorUI
0x00007ff92062c000 	/System/Library/Frameworks/WebKit.framework/Versions/A/Frameworks/WebCore.framework/Versions/A/WebCore
0x00007ffb298dc000 	/System/Library/Frameworks/WebKit.framework/Versions/A/Frameworks/WebCore.framework/Versions/A/Frameworks/libANGLE-shared.dylib
0x00007ffd20eb9000 	/System/Library/PrivateFrameworks/WebGPU.framework/Versions/A/WebGPU
0x00007ff92893d000 	/System/Library/Frameworks/SceneKit.framework/Versions/A/SceneKit
0x00007ff938473000 	/System/Library/Frameworks/ModelIO.framework/Versions/A/ModelIO
0x00007ff82fc62000 	/System/Library/Frameworks/MetalKit.framework/Versions/A/MetalKit
0x00007ff831d6e000 	/System/Library/Frameworks/GLKit.framework/Versions/A/GLKit
0x00007ffd29ca6000 	/usr/lib/usd/libusd_ms.dylib
0x00007ffd1d89a000 	/System/Library/PrivateFrameworks/USDLib_FormatLoaderProxy.framework/Versions/A/USDLib_FormatLoaderProxy
0x00007ff925d23000 	/System/Library/PrivateFrameworks/CoreOptimization.framework/Versions/A/CoreOptimization
0x00007ff934468000 	/System/Library/PrivateFrameworks/StorageKit.framework/Versions/A/StorageKit
0x00007ffb23b14000 	/System/Library/Frameworks/FSKit.framework/Versions/A/FSKit
0x00007ffb35789000 	/System/Library/PrivateFrameworks/DiskImages2.framework/Versions/A/DiskImages2
0x00007ffc27220000 	/System/Library/PrivateFrameworks/LiveFS.framework/Versions/A/LiveFS
0x00007ffd263cf000 	/System/Library/PrivateFrameworks/icloudMCCKit.framework/Versions/A/icloudMCCKit
0x00007ffb2d814000 	/System/Library/PrivateFrameworks/AppleAccountUI.framework/Versions/A/AppleAccountUI
0x00007ffb30e22000 	/System/Library/PrivateFrameworks/CloudSubscriptionFeatures.framework/Versions/A/CloudSubscriptionFeatures
0x00007ffa24e4f000 	/System/Library/PrivateFrameworks/PreferencePanesSupport.framework/Versions/A/PreferencePanesSupport
0x00007ff92d050000 	/System/Library/PrivateFrameworks/FindMyDeviceUI.framework/Versions/A/FindMyDeviceUI
0x00007ffd24056000 	/System/Library/PrivateFrameworks/iCloudQuotaUI.framework/Versions/A/iCloudQuotaUI
0x00007ffa25009000 	/System/Library/PrivateFrameworks/CoreCDPUI.framework/Versions/A/CoreCDPUI
0x00007ff927788000 	/System/Library/PrivateFrameworks/CoreFollowUpUI.framework/Versions/A/CoreFollowUpUI
0x00007ff82e332000 	/System/Library/PrivateFrameworks/CoreFollowUp.framework/Versions/A/CoreFollowUp
0x00007ff9332a9000 	/System/Library/PrivateFrameworks/IMCore.framework/Versions/A/IMCore
0x00007ff91dccf000 	/System/Library/PrivateFrameworks/CoreCDP.framework/Versions/A/CoreCDP
0x00007ff838c08000 	/System/Library/PrivateFrameworks/AskPermission.framework/Versions/A/AskPermission
0x00007ff92b126000 	/System/Library/Frameworks/Quartz.framework/Versions/A/Quartz
0x00007ff936f53000 	/System/Library/PrivateFrameworks/CloudPhotoServicesConfiguration.framework/Versions/A/CloudPhotoServicesConfiguration
0x00007ff9299e7000 	/System/Library/PrivateFrameworks/CommerceKit.framework/Versions/A/CommerceKit
0x00007ff937dba000 	/System/Library/PrivateFrameworks/FindMyMac.framework/Versions/A/FindMyMac
0x00007ff937c9b000 	/System/Library/Frameworks/PreferencePanes.framework/Versions/A/PreferencePanes
0x00007ff92c5d5000 	/System/Library/Frameworks/AddressBook.framework/Versions/A/AddressBook
0x00007ff837b96000 	/System/Library/PrivateFrameworks/AOSAccounts.framework/Versions/A/AOSAccounts
0x00007ff832191000 	/System/Library/PrivateFrameworks/AppleSRP.framework/Versions/A/AppleSRP
0x00007ff925ecb000 	/System/Library/Frameworks/SafariServices.framework/Versions/A/SafariServices
0x00007ff8365f4000 	/System/Library/PrivateFrameworks/NetFSServer.framework/Versions/A/NetFSServer
0x00007ff928808000 	/System/Library/Frameworks/PDFKit.framework/Versions/A/PDFKit
0x00007ff938d01000 	/System/Library/PrivateFrameworks/EmailFoundation.framework/Versions/A/EmailFoundation
0x00007ff937cf3000 	/System/Library/PrivateFrameworks/EmailCore.framework/Versions/A/EmailCore
0x00007ff93b9f6000 	/System/Library/PrivateFrameworks/EmailAddressing.framework/Versions/A/EmailAddressing
0x00007ff93ba02000 	/System/Library/PrivateFrameworks/Email.framework/Versions/A/Email
0x00007ffb2eb02000 	/System/Library/PrivateFrameworks/BackgroundSystemTasks.framework/Versions/A/BackgroundSystemTasks
0x00007ff92770c000 	/System/Library/PrivateFrameworks/IntlPreferences.framework/Versions/A/IntlPreferences
0x00007ffc273bc000 	/System/Library/PrivateFrameworks/LockdownMode.framework/Versions/A/LockdownMode
0x00007ff832c15000 	/System/Library/PrivateFrameworks/CommunicationsFilter.framework/Versions/A/CommunicationsFilter
0x00007ffb2e9c1000 	/System/Library/PrivateFrameworks/AvatarKit.framework/Versions/A/AvatarKit
0x00007ffb2ea81000 	/System/Library/PrivateFrameworks/AvatarPersistence.framework/Versions/A/AvatarPersistence
0x00007ffc30775000 	/System/Library/PrivateFrameworks/RemoteUI.framework/Versions/A/RemoteUI
0x00007ff831e3c000 	/usr/lib/libCTGreenTeaLogger.dylib
0x00007ffb2936e000 	/System/Library/Frameworks/TipKit.framework/Versions/A/TipKit
0x00007ffa26f32000 	/System/Library/PrivateFrameworks/AppleCVA.framework/Versions/A/AppleCVA
0x00007ffb2ea80000 	/System/Library/PrivateFrameworks/AvatarKitContent.framework/Versions/A/AvatarKitContent
0x00007ff831e68000 	/System/Library/PrivateFrameworks/CoreAppleCVA.framework/Versions/A/CoreAppleCVA
0x00007ffc2659f000 	/System/Library/PrivateFrameworks/LiftUI.framework/Versions/A/LiftUI
0x00007ffb23adc000 	/System/Library/Frameworks/ExtensionKit.framework/Versions/A/ExtensionKit
0x00007ffb30d9d000 	/System/Library/PrivateFrameworks/CloudSettings.framework/Versions/A/CloudSettings
0x00007ff83840c000 	/System/Library/Frameworks/EventKit.framework/Versions/A/EventKit
0x00007ff926d96000 	/System/Library/PrivateFrameworks/SearchFoundation.framework/Versions/A/SearchFoundation
0x00007ff92598b000 	/System/Library/PrivateFrameworks/CoreRoutine.framework/Versions/A/CoreRoutine
0x00007ffb3014f000 	/System/Library/PrivateFrameworks/CalendarDatabase.framework/Versions/A/CalendarDatabase
0x00007ffb300a5000 	/System/Library/PrivateFrameworks/CalendarDaemon.framework/Versions/A/CalendarDaemon
0x00007ff837842000 	/System/Library/PrivateFrameworks/CalendarFoundation.framework/Versions/A/CalendarFoundation
0x00007ff8377fd000 	/System/Library/PrivateFrameworks/iCalendar.framework/Versions/A/iCalendar
0x00007ffb35306000 	/System/Library/PrivateFrameworks/DataAccessExpress.framework/Versions/A/DataAccessExpress
0x00007ff92ccf1000 	/System/Library/PrivateFrameworks/FTClientServices.framework/Versions/A/FTClientServices
0x00007ff91f8ea000 	/System/Library/PrivateFrameworks/CoreDAV.framework/Versions/A/CoreDAV
0x00007ff8382ee000 	/System/Library/PrivateFrameworks/CalDAV.framework/Versions/A/CalDAV
0x00007ff9239ae000 	/System/Library/PrivateFrameworks/TelephonyUtilities.framework/Versions/A/TelephonyUtilities
0x00007ff92783d000 	/System/Library/Frameworks/CallKit.framework/Versions/A/CallKit
0x00007ffa2db0f000 	/System/Library/PrivateFrameworks/IMTransferServices.framework/Versions/A/IMTransferServices
0x00007ff925d3d000 	/System/Library/PrivateFrameworks/IncomingCallFilter.framework/Versions/A/IncomingCallFilter
0x00007ffc2ab22000 	/System/Library/PrivateFrameworks/NeighborhoodActivityConduit.framework/Versions/A/NeighborhoodActivityConduit
0x00007ffd28a3f000 	/usr/lib/libBASupport.dylib
0x00007ff933039000 	/System/Library/PrivateFrameworks/SpotlightReceiver.framework/Versions/A/SpotlightReceiver
0x00007ffb30fc1000 	/System/Library/PrivateFrameworks/Coherence.framework/Versions/A/Coherence
0x00007ff9238b7000 	/System/Library/PrivateFrameworks/CoreParsec.framework/Versions/A/CoreParsec
0x00007ffc2cee0000 	/System/Library/PrivateFrameworks/PegasusAPI.framework/Versions/A/PegasusAPI
0x00007ffc2d975000 	/System/Library/PrivateFrameworks/PegasusConfiguration.framework/Versions/A/PegasusConfiguration
0x00007ffc2739d000 	/System/Library/PrivateFrameworks/LocalStatusKit.framework/Versions/A/LocalStatusKit
0x00007ff92ccf5000 	/System/Library/PrivateFrameworks/CharacterPicker.framework/Versions/A/CharacterPicker
0x00007ff925e3f000 	/System/Library/PrivateFrameworks/ContactsDonation.framework/Versions/A/ContactsDonation
0x00007ff91fc4c000 	/System/Library/PrivateFrameworks/ContactsUICore.framework/Versions/A/ContactsUICore
0x00007ff925769000 	/System/Library/PrivateFrameworks/DataDetectors.framework/Versions/A/DataDetectors
0x00007ff8382dc000 	/System/Library/Frameworks/MediaLibrary.framework/Versions/A/MediaLibrary
0x00007ff832c06000 	/System/Library/PrivateFrameworks/PersonaKit.framework/Versions/A/PersonaKit
0x00007ff91fc3b000 	/System/Library/PrivateFrameworks/PersonaUI.framework/Versions/A/PersonaUI
0x00007ffc38b28000 	/System/Library/PrivateFrameworks/Stickers.framework/Versions/A/Stickers
0x00007ffc23acc000 	/System/Library/PrivateFrameworks/InputAnalytics.framework/Versions/A/InputAnalytics
0x00007ff92e34f000 	/System/Library/PrivateFrameworks/EmojiFoundation.framework/Versions/A/EmojiFoundation
0x00007ffb37456000 	/System/Library/PrivateFrameworks/FeedbackService.framework/Versions/A/FeedbackService
0x00007ff8328a5000 	/System/Library/PrivateFrameworks/AddressBookCore.framework/Versions/A/AddressBookCore
0x00007ff92c744000 	/System/Library/PrivateFrameworks/ToneKit.framework/Versions/A/ToneKit
0x00007ff927750000 	/System/Library/PrivateFrameworks/ToneLibrary.framework/Versions/A/ToneLibrary
0x00007ffb316e6000 	/System/Library/PrivateFrameworks/ContactsAccounts.framework/Versions/A/ContactsAccounts
0x00007ffb284b7000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x00007ff9376bc000 	/System/Library/PrivateFrameworks/Transparency.framework/Versions/A/Transparency
0x00007ff8377ef000 	/System/Library/PrivateFrameworks/CoreRecents.framework/Versions/A/CoreRecents
0x00007ffb2c4d4000 	/System/Library/PrivateFrameworks/AppIntentsServices.framework/Versions/A/AppIntentsServices
0x00007ffa24937000 	/System/Library/PrivateFrameworks/DiagnosticRequest.framework/Versions/A/DiagnosticRequest
0x00007ff92b04a000 	/System/Library/PrivateFrameworks/Lookup.framework/Versions/A/Lookup
0x00007ffb2768e000 	/System/Library/Frameworks/QuickLookUI.framework/Versions/A/QuickLookUI
0x00007ffb28034000 	/System/Library/Frameworks/ScreenCaptureKit.framework/Versions/A/ScreenCaptureKit
0x00007ff92833c000 	/System/Library/Frameworks/Quartz.framework/Versions/A/Frameworks/QuartzComposer.framework/Versions/A/QuartzComposer
0x00007ff9276e4000 	/System/Library/Frameworks/Quartz.framework/Versions/A/Frameworks/QuartzFilters.framework/Versions/A/QuartzFilters
0x00007ff92672d000 	/System/Library/Frameworks/Quartz.framework/Versions/A/Frameworks/ImageKit.framework/Versions/A/ImageKit
0x00007ff924ecc000 	/System/Library/PrivateFrameworks/CorePDF.framework/Versions/A/CorePDF
0x00007ff82fc00000 	/System/Library/Frameworks/QuickLook.framework/Versions/A/QuickLook
0x00007ff839f2a000 	/System/Library/PrivateFrameworks/DisplayServices.framework/Versions/A/DisplayServices
0x00007ff92764d000 	/System/Library/Frameworks/ImageCaptureCore.framework/Versions/A/ImageCaptureCore
0x00007ffd1fa7b000 	/System/Library/PrivateFrameworks/VisionKitCore.framework/Versions/A/VisionKitCore
0x00007ff923888000 	/System/Library/PrivateFrameworks/QuickLookSupport.framework/Versions/A/QuickLookSupport
0x00007ff925764000 	/System/Library/PrivateFrameworks/QuickLookNonBaseSystem.framework/Versions/A/QuickLookNonBaseSystem
0x00007ffa28be7000 	/System/Library/Frameworks/ReplayKit.framework/Versions/A/ReplayKit
0x00007ffc3938f000 	/System/Library/PrivateFrameworks/Symptoms.framework/Versions/A/Frameworks/SymptomPresentationFeed.framework/Versions/A/SymptomPresentationFeed
0x00007ffc3910f000 	/System/Library/PrivateFrameworks/SymptomShared.framework/Versions/A/SymptomShared
0x00007ff83ae4b000 	/System/Library/PrivateFrameworks/MarkupUI.framework/Versions/A/MarkupUI
0x00007ff92573f000 	/System/Library/PrivateFrameworks/SidecarCore.framework/Versions/A/SidecarCore
0x00007ff9281eb000 	/System/Library/PrivateFrameworks/AnnotationKit.framework/Versions/A/AnnotationKit
0x00007ffb294ae000 	/System/Library/Frameworks/Translation.framework/Versions/A/Translation
0x00007ffc28b99000 	/System/Library/PrivateFrameworks/MediaAnalysisServices.framework/Versions/A/MediaAnalysisServices
0x00007ffa20bf1000 	/System/Library/PrivateFrameworks/RevealCore.framework/Versions/A/RevealCore
0x00007ffa20be6000 	/System/Library/PrivateFrameworks/Reveal.framework/Versions/A/Reveal
0x00007ffd1c9c5000 	/System/Library/PrivateFrameworks/TranslationUIServices.framework/Versions/A/TranslationUIServices
0x00007ff92698e000 	/System/Library/Frameworks/PencilKit.framework/Versions/A/PencilKit
0x00007ff926b59000 	/System/Library/PrivateFrameworks/SidecarUI.framework/Versions/A/SidecarUI
0x00007ff92627e000 	/System/Library/PrivateFrameworks/CoreHandwriting.framework/Versions/A/CoreHandwriting
0x00007ff81d0be000 	/usr/lib/libmecabra.dylib
0x00007ff920084000 	/System/Library/PrivateFrameworks/Calculate.framework/Versions/A/Calculate
0x00007ff82bb6a000 	/usr/lib/libChineseTokenizer.dylib
0x00007ffa372a5000 	/System/Library/PrivateFrameworks/ScreenReaderCore.framework/Versions/A/ScreenReaderCore
0x00007ffc38e92000 	/System/Library/PrivateFrameworks/StocksKit.framework/Versions/A/StocksKit
0x00007ff91ed2f000 	/System/Library/PrivateFrameworks/OAuth.framework/Versions/A/OAuth
0x00007ffc38b98000 	/System/Library/PrivateFrameworks/StocksCore.framework/Versions/A/StocksCore
0x00007ffc39970000 	/System/Library/PrivateFrameworks/TeaFoundation.framework/Versions/A/TeaFoundation
0x00007ffc39aed000 	/System/Library/PrivateFrameworks/TeaSettings.framework/Versions/A/TeaSettings
0x00007ffa2ec41000 	/System/Library/PrivateFrameworks/NewsCore.framework/Versions/A/NewsCore
0x00007ffc3992b000 	/System/Library/PrivateFrameworks/TeaDB.framework/Versions/A/TeaDB
0x00007ffa2c002000 	/System/Library/PrivateFrameworks/RemoteConfiguration.framework/Versions/A/RemoteConfiguration
0x00007ffa2e884000 	/System/Library/PrivateFrameworks/NewsTransport.framework/Versions/A/NewsTransport
0x00007ffa2ec33000 	/System/Library/PrivateFrameworks/NewsFoundation.framework/Versions/A/NewsFoundation
0x00007ffc2af48000 	/System/Library/PrivateFrameworks/NewsURLBucket.framework/Versions/A/NewsURLBucket
0x00007ff929c0c000 	/System/Library/PrivateFrameworks/MobileInstallation.framework/Versions/A/MobileInstallation
0x00007ff932a49000 	/System/Library/PrivateFrameworks/MMCS.framework/Versions/A/MMCS
0x00007ff838143000 	/System/Library/PrivateFrameworks/ChunkingLibrary.framework/Versions/A/ChunkingLibrary
0x00007ff83213e000 	/System/Library/PrivateFrameworks/AssetCacheServices.framework/Versions/A/AssetCacheServices
0x00007ff929fb9000 	/System/Library/PrivateFrameworks/PhotoLibraryServices.framework/Versions/A/PhotoLibraryServices
0x00007ff927282000 	/System/Library/Frameworks/Photos.framework/Versions/A/Photos
0x00007ff939ebe000 	/System/Library/PrivateFrameworks/PhotosFormats.framework/Versions/A/PhotosFormats
0x00007ff936f83000 	/System/Library/PrivateFrameworks/CloudPhotoLibrary.framework/Versions/A/CloudPhotoLibrary
0x00007ffa1f33d000 	/System/Library/PrivateFrameworks/CloudPhotoServices.framework/Versions/A/CloudPhotoServices
0x00007ff93bb5d000 	/System/Library/PrivateFrameworks/CoreMediaStream.framework/Versions/A/CoreMediaStream
0x00007ff8335ae000 	/System/Library/PrivateFrameworks/CPAnalytics.framework/Versions/A/CPAnalytics
0x00007ff938381000 	/System/Library/PrivateFrameworks/MediaConversionService.framework/Versions/A/MediaConversionService
0x00007ff839bf4000 	/System/Library/PrivateFrameworks/PhotoFoundation.framework/Versions/A/PhotoFoundation
0x00007ffc2dde4000 	/System/Library/PrivateFrameworks/PhotoLibraryServicesCore.framework/Versions/A/PhotoLibraryServicesCore
0x00007ff8340be000 	/System/Library/PrivateFrameworks/AVFoundationCF.framework/Versions/A/AVFoundationCF
0x00007ffa2da0f000 	/usr/lib/swift/libswiftDemangle.dylib
0x00007ff9259e9000 	/System/Library/PrivateFrameworks/MediaStream.framework/Versions/A/MediaStream
0x00007ff9388d7000 	/System/Library/PrivateFrameworks/NeutrinoCore.framework/Versions/A/NeutrinoCore
0x00007ff8230ee000 	/System/Library/PrivateFrameworks/NLP.framework/Versions/A/NLP
0x00007ffa1e0a9000 	/System/Library/PrivateFrameworks/PhotoImaging.framework/Versions/A/PhotoImaging
0x00007ffa1f2fd000 	/System/Library/PrivateFrameworks/PhotosImagingFoundation.framework/Versions/A/PhotosImagingFoundation
0x00007ffc2e898000 	/System/Library/PrivateFrameworks/PhotosIntelligenceCore.framework/Versions/A/PhotosIntelligenceCore
0x00007ff834557000 	/System/Library/PrivateFrameworks/EmbeddedAcousticRecognition.framework/Versions/A/EmbeddedAcousticRecognition
0x00007ff9371ea000 	/System/Library/PrivateFrameworks/SDAPI.framework/Versions/A/SDAPI
0x00007ffa1f34a000 	/System/Library/PrivateFrameworks/AutoLoop.framework/Versions/A/AutoLoop
0x00007ffc2f17a000 	/System/Library/PrivateFrameworks/Portrait.framework/Versions/A/Portrait
0x00007ffb2d8a8000 	/System/Library/PrivateFrameworks/AppleDepth.framework/Versions/A/AppleDepth
0x00007ffb37db3000 	/System/Library/PrivateFrameworks/FusionTracker.framework/Versions/A/FusionTracker
0x00007ffb2d9e1000 	/System/Library/PrivateFrameworks/AppleDepthCore.framework/Versions/A/AppleDepthCore
0x00007ff927e01000 	/System/Library/PrivateFrameworks/iTunesCloud.framework/Versions/A/iTunesCloud
0x00007ff930266000 	/System/Library/PrivateFrameworks/DAAPKit.framework/Versions/A/DAAPKit
0x00007ffd297cb000 	/usr/lib/swift/libswiftVideoToolbox.dylib
0x00007ffc3bb2c000 	/System/Library/PrivateFrameworks/TipKitCore.framework/Versions/A/TipKitCore
0x00007ff92e3d4000 	/System/Library/PrivateFrameworks/CoreCDPInternal.framework/Versions/A/CoreCDPInternal
0x00007ff91ec7d000 	/System/Library/PrivateFrameworks/AccountsDaemon.framework/Versions/A/AccountsDaemon
0x00007ffc30459000 	/System/Library/PrivateFrameworks/RegulatoryDomain.framework/Versions/A/RegulatoryDomain
0x00007ffa25006000 	/System/Library/PrivateFrameworks/MachineSettings.framework/Versions/A/MachineSettings
0x00007ff937c20000 	/System/Library/PrivateFrameworks/BezelServices.framework/Versions/A/BezelServices
0x00007ff9302ef000 	/System/Library/PrivateFrameworks/SPOwner.framework/Versions/A/SPOwner
0x00007ff929a4d000 	/System/Library/PrivateFrameworks/FMCore.framework/Versions/A/FMCore
0x00007ffd23fbb000 	/System/Library/PrivateFrameworks/iCloudQuota.framework/Versions/A/iCloudQuota
0x00007ff91fbb0000 	/System/Library/PrivateFrameworks/CoreRecognition.framework/Versions/A/CoreRecognition
0x00007ffc2c99b000 	/System/Library/PrivateFrameworks/PassKitMacHelperTemp.framework/Versions/A/PassKitMacHelperTemp
0x00007ff937cd0000 	/System/Library/PrivateFrameworks/InAppMessages.framework/Versions/A/InAppMessages
0x00007ffa36130000 	/System/Library/PrivateFrameworks/JetUI.framework/Versions/A/JetUI
0x00007ffc26336000 	/System/Library/PrivateFrameworks/JetPack.framework/Versions/A/JetPack
0x00007ffa2b201000 	/System/Library/PrivateFrameworks/PassKitUIFoundation.framework/Versions/A/PassKitUIFoundation
0x00007ff83a020000 	/System/Library/PrivateFrameworks/PassKitCore.framework/Versions/A/PassKitCore
0x00007ff925fcd000 	/System/Library/PrivateFrameworks/BiometricKit.framework/Versions/A/BiometricKit
0x00007ffc30ff7000 	/System/Library/PrivateFrameworks/SEService.framework/Versions/A/SEService
0x00007ff92b15d000 	/System/Library/PrivateFrameworks/NearField.framework/Versions/A/NearField
0x00007ffd28b9d000 	/usr/lib/libSESShared.dylib
0x00007ffc3117d000 	/System/Library/PrivateFrameworks/STSXPCHelperClient.framework/Versions/A/STSXPCHelperClient
0x00007ff92c972000 	/usr/lib/libnfshared.dylib
0x00007ff937cc8000 	/System/Library/PrivateFrameworks/InAppMessagesCore.framework/Versions/A/InAppMessagesCore
0x00007ffd23742000 	/System/Library/PrivateFrameworks/_JetEngine_SwiftUI.framework/Versions/A/_JetEngine_SwiftUI
0x00007ffc393b7000 	/System/Library/PrivateFrameworks/Synapse.framework/Versions/A/Synapse
0x00007ffb2ff74000 	/System/Library/PrivateFrameworks/CSExattrCrypto.framework/Versions/A/CSExattrCrypto
0x00007ff839c01000 	/System/Library/PrivateFrameworks/IMSharedUtilities.framework/Versions/A/IMSharedUtilities
0x00007ff93395f000 	/System/Library/Frameworks/InstantMessage.framework/Versions/A/InstantMessage
0x00007ff92c4da000 	/System/Library/PrivateFrameworks/IDSKVStore.framework/Versions/A/IDSKVStore
0x00007ff92c31e000 	/System/Library/PrivateFrameworks/IMDPersistence.framework/Versions/A/IMDPersistence
0x00007ffd296ca000 	/usr/lib/swift/libswiftAppleArchive.dylib
0x00007ffd1e01d000 	/System/Library/PrivateFrameworks/UserSafety.framework/Versions/A/UserSafety
0x00007ff831a7a000 	/System/Library/Frameworks/MapKit.framework/Versions/A/MapKit
0x00007ffa2a5e2000 	/System/Library/PrivateFrameworks/BlastDoor.framework/Versions/A/BlastDoor
0x00007ffc28323000 	/System/Library/PrivateFrameworks/ManagedSettingsObjC.framework/Versions/A/ManagedSettingsObjC
0x00007ffb2808a000 	/System/Library/Frameworks/SensitiveContentAnalysis.framework/Versions/A/SensitiveContentAnalysis
0x00007ffc211d2000 	/System/Library/PrivateFrameworks/GeoAnalytics.framework/Versions/A/GeoAnalytics
0x00007ff92c9a6000 	/System/Library/PrivateFrameworks/Navigation.framework/Versions/A/Navigation
0x00007ff9357be000 	/System/Library/PrivateFrameworks/VectorKit.framework/Versions/A/VectorKit
0x00007ff91fb19000 	/System/Library/PrivateFrameworks/VirtualGarage.framework/Versions/A/VirtualGarage
0x00007ff92cb37000 	/System/Library/Frameworks/ExternalAccessory.framework/ExternalAccessory
0x00007ff92cb50000 	/System/Library/PrivateFrameworks/IAP.framework/Versions/A/IAP
0x00007ff831d39000 	/System/Library/PrivateFrameworks/IOPlatformPluginFamily.framework/Versions/A/IOPlatformPluginFamily
0x00007ffc270b2000 	/System/Library/PrivateFrameworks/LinkPresentationStyleSheetParsing.framework/Versions/A/LinkPresentationStyleSheetParsing
0x00007ff925d9b000 	/System/Library/PrivateFrameworks/CoreSDB.framework/Versions/A/CoreSDB
0x00007ffb2e54f000 	/System/Library/PrivateFrameworks/AskToCore.framework/Versions/A/AskToCore
0x00007ff92ad50000 	/System/Library/PrivateFrameworks/StoreFoundation.framework/Versions/A/StoreFoundation
0x00007ff929a47000 	/System/Library/PrivateFrameworks/ServerInformation.framework/Versions/A/ServerInformation
0x00007ff9294c3000 	/System/Library/PrivateFrameworks/Install.framework/Frameworks/DistributionKit.framework/Versions/A/DistributionKit
0x00007ff923647000 	/System/Library/PrivateFrameworks/PackageKit.framework/Versions/A/PackageKit
0x00007ff9294e3000 	/System/Library/PrivateFrameworks/OSPersonalization.framework/Versions/A/OSPersonalization
0x00007ff92b221000 	/System/Library/PrivateFrameworks/BridgeOSSoftwareUpdate.framework/Versions/A/BridgeOSSoftwareUpdate
0x00007ff9261ba000 	/System/Library/PrivateFrameworks/StorageManagement.framework/Versions/A/StorageManagement
0x00007ff939021000 	/usr/lib/libauthinstall.dylib
0x00007ff92b229000 	/System/Library/PrivateFrameworks/InstallerDiagnostics.framework/Versions/A/InstallerDiagnostics
0x00007ff9395c9000 	/usr/lib/libcrypto.42.dylib
0x00007ff92b210000 	/System/Library/PrivateFrameworks/GPUInfo.framework/Versions/A/GPUInfo
0x00007ff937dc0000 	/System/Library/PrivateFrameworks/MobileActivationMacOS.framework/Versions/A/MobileActivationMacOS
0x00007ffb238f8000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x00007ffd296ea000 	/usr/lib/swift/libswiftDataDetection.dylib
0x00007ff833557000 	/System/Library/PrivateFrameworks/GenerationalStorage.framework/Versions/A/GenerationalStorage
0x00007ff82e725000 	/System/Library/PrivateFrameworks/CacheDelete.framework/Versions/A/CacheDelete
0x00007ff836719000 	/System/Library/Frameworks/QuickLookThumbnailing.framework/Versions/A/QuickLookThumbnailing
0x00007ff83338e000 	/System/Library/Frameworks/FileProvider.framework/Versions/A/FileProvider
0x00007ff8213a4000 	/System/Library/PrivateFrameworks/DesktopServicesPriv.framework/Versions/A/DesktopServicesPriv
0x00007ff823853000 	/System/Library/Frameworks/Accounts.framework/Versions/A/Accounts
0x00007ff92b270000 	/System/Library/PrivateFrameworks/AOSKit.framework/Versions/A/AOSKit
0x00007ff82bd99000 	/System/Library/PrivateFrameworks/AppSSOCore.framework/Versions/A/AppSSOCore
0x00007ff835f14000 	/System/Library/PrivateFrameworks/AppSupport.framework/Versions/A/AppSupport
0x00007ff92526b000 	/System/Library/Frameworks/AVFoundation.framework/Versions/A/AVFoundation
0x00007ff836649000 	/System/Library/PrivateFrameworks/DuetActivityScheduler.framework/Versions/A/DuetActivityScheduler
0x00007ff925d43000 	/System/Library/PrivateFrameworks/FTServices.framework/Versions/A/FTServices
0x00007ff936e39000 	/System/Library/PrivateFrameworks/NetworkServiceProxy.framework/Versions/A/NetworkServiceProxy
0x00007ff83660c000 	/System/Library/PrivateFrameworks/StreamingZip.framework/Versions/A/StreamingZip
0x00007ff835888000 	/System/Library/PrivateFrameworks/CoreWiFi.framework/Versions/A/CoreWiFi
0x00007ff82e654000 	/System/Library/PrivateFrameworks/MobileAsset.framework/Versions/A/MobileAsset
0x00007ff834499000 	/System/Library/PrivateFrameworks/Rapport.framework/Versions/A/Rapport
0x00007ffc3911e000 	/System/Library/PrivateFrameworks/Symptoms.framework/Versions/A/Frameworks/SymptomAnalytics.framework/Versions/A/SymptomAnalytics
0x00007ff831da7000 	/usr/lib/libnetworkextension.dylib
0x00007ffd2917c000 	/usr/lib/libmrc.dylib
0x00007ffc23a6a000 	/System/Library/PrivateFrameworks/IPConfiguration.framework/Versions/A/IPConfiguration
0x00007ffa24e48000 	/System/Library/PrivateFrameworks/Netrb.framework/Versions/A/Netrb
0x00007ffa2f560000 	/System/Library/PrivateFrameworks/WiFiPeerToPeer.framework/Versions/A/WiFiPeerToPeer
0x00007ff92cc77000 	/System/Library/PrivateFrameworks/SoftwareUpdateCoreSupport.framework/Versions/A/SoftwareUpdateCoreSupport
0x00007ff934453000 	/System/Library/PrivateFrameworks/SoftwareUpdateCoreConnect.framework/Versions/A/SoftwareUpdateCoreConnect
0x00007ff835dc7000 	/System/Library/PrivateFrameworks/RemoteServiceDiscovery.framework/Versions/A/RemoteServiceDiscovery
0x00007ff92c8e5000 	/System/Library/PrivateFrameworks/MSUDataAccessor.framework/Versions/A/MSUDataAccessor
0x00007ff9281da000 	/usr/lib/libbootpolicy.dylib
0x00007ff835ddc000 	/System/Library/PrivateFrameworks/RemoteXPC.framework/Versions/A/RemoteXPC
0x00007ff93396e000 	/usr/lib/libFDR.dylib
0x00007ff9390f2000 	/usr/lib/libamsupport.dylib
0x00007ffb2da51000 	/System/Library/PrivateFrameworks/AppleDeviceQuerySupport.framework/Versions/A/AppleDeviceQuerySupport
0x00007ff93be79000 	/usr/lib/libpartition2_dynamic.dylib
0x00007ff82a188000 	/System/Library/Frameworks/UserNotifications.framework/Versions/A/UserNotifications
0x00007ff82e7b9000 	/usr/lib/libTelephonyUtilDynamic.dylib
0x00007ff832d98000 	/System/Library/PrivateFrameworks/SymptomDiagnosticReporter.framework/Versions/A/SymptomDiagnosticReporter
0x00007ffa2afe2000 	/System/Library/Frameworks/CryptoKit.framework/Versions/A/CryptoKit
0x00007ff8365e8000 	/System/Library/PrivateFrameworks/CryptoKitCBridging.framework/Versions/A/CryptoKitCBridging
0x00007ff833fdf000 	/System/Library/Frameworks/CryptoTokenKit.framework/Versions/A/CryptoTokenKit
0x00007ffa1f8d0000 	/usr/lib/swift/libswiftCryptoTokenKit.dylib
0x00007ff83403f000 	/System/Library/Frameworks/LocalAuthentication.framework/Versions/A/LocalAuthentication
0x00007ffc2726f000 	/System/Library/PrivateFrameworks/LocalAuthenticationCore.framework/Versions/A/LocalAuthenticationCore
0x00007ff83408e000 	/System/Library/Frameworks/LocalAuthentication.framework/Support/SharedUtils.framework/Versions/A/SharedUtils
0x00007ff825b67000 	/System/Library/PrivateFrameworks/ApplePushService.framework/Versions/A/ApplePushService
0x00007ff925489000 	/System/Library/PrivateFrameworks/FindMyDevice.framework/Versions/A/FindMyDevice
0x00007ff8238d8000 	/System/Library/PrivateFrameworks/CommonUtilities.framework/Versions/A/CommonUtilities
0x00007ff93a9d7000 	/System/Library/PrivateFrameworks/DeviceIdentity.framework/Versions/A/DeviceIdentity
0x00007ff82c4c6000 	/System/Library/PrivateFrameworks/Bom.framework/Versions/A/Bom
0x00007ff82e292000 	/usr/lib/libParallelCompression.dylib
0x00007ffd2890e000 	/usr/lib/libAppleArchive.dylib
0x00007ff82e285000 	/System/Library/PrivateFrameworks/MultiverseSupport.framework/Versions/A/MultiverseSupport
0x00007ff83a004000 	/System/Library/PrivateFrameworks/FMCoreLite.framework/Versions/A/FMCoreLite
0x00007ff829fb0000 	/System/Library/PrivateFrameworks/AuthKit.framework/Versions/A/AuthKit
0x00007ff832eb8000 	/System/Library/PrivateFrameworks/DiskManagement.framework/Versions/A/DiskManagement
0x00007ff832dd4000 	/System/Library/PrivateFrameworks/AppleIDAuthSupport.framework/Versions/A/AppleIDAuthSupport
0x00007ffb2a4a2000 	/System/Library/PrivateFrameworks/AAAFoundation.framework/Versions/A/AAAFoundation
0x00007ff82e774000 	/System/Library/PrivateFrameworks/KeychainCircle.framework/Versions/A/KeychainCircle
0x00007ff920555000 	/System/Library/PrivateFrameworks/URLFormatting.framework/Versions/A/URLFormatting
0x00007ff835e80000 	/usr/lib/libcsfde.dylib
0x00007ff82bf2a000 	/usr/lib/libCoreStorage.dylib
0x00007ff82fb77000 	/System/Library/PrivateFrameworks/ProtectedCloudStorage.framework/Versions/A/ProtectedCloudStorage
0x00007ff835e7c000 	/System/Library/PrivateFrameworks/EFILogin.framework/Versions/A/EFILogin
0x00007ff91f302000 	/System/Library/PrivateFrameworks/CloudServices.framework/Versions/A/CloudServices
0x00007ff833580000 	/System/Library/PrivateFrameworks/OctagonTrust.framework/Versions/A/OctagonTrust
0x00007ff833bf8000 	/System/Library/PrivateFrameworks/AVFCore.framework/Versions/A/AVFCore
0x00007ff91dd52000 	/System/Library/PrivateFrameworks/AVFCapture.framework/Versions/A/AVFCapture
0x00007ff82cfbe000 	/System/Library/Frameworks/MediaToolbox.framework/Versions/A/MediaToolbox
0x00007ff83335f000 	/System/Library/PrivateFrameworks/CoreAVCHD.framework/Versions/A/CoreAVCHD
0x00007ff83335b000 	/System/Library/PrivateFrameworks/Mangrove.framework/Versions/A/Mangrove
0x00007ffb2fe79000 	/System/Library/PrivateFrameworks/CMPhoto.framework/Versions/A/CMPhoto
0x00007ff833a49000 	/System/Library/Frameworks/CoreTelephony.framework/Versions/A/CoreTelephony
0x00007ff834214000 	/System/Library/PrivateFrameworks/AppleVPA.framework/Versions/A/AppleVPA
0x00007ff832fa0000 	/System/Library/PrivateFrameworks/CoreAUC.framework/Versions/A/CoreAUC
0x00007ffb2ddb5000 	/System/Library/PrivateFrameworks/AppleJPEGXL.framework/Versions/A/AppleJPEGXL
0x00007ffb2fd76000 	/System/Library/PrivateFrameworks/CMImaging.framework/Versions/A/CMImaging
0x00007ff91df6a000 	/System/Library/PrivateFrameworks/Quagga.framework/Versions/A/Quagga
0x00007ff91e09b000 	/System/Library/PrivateFrameworks/CMCapture.framework/Versions/A/CMCapture
0x00007ff82e362000 	/System/Library/Frameworks/CoreMediaIO.framework/Versions/A/CoreMediaIO
0x00007ff836760000 	/System/Library/PrivateFrameworks/Espresso.framework/Versions/A/Espresso
0x00007ffa37620000 	/System/Library/PrivateFrameworks/ANECompiler.framework/Versions/A/ANECompiler
0x00007ff838849000 	/System/Library/PrivateFrameworks/AppleNeuralEngine.framework/Versions/A/AppleNeuralEngine
0x00007ffb25896000 	/System/Library/Frameworks/MetalPerformanceShadersGraph.framework/Versions/A/MetalPerformanceShadersGraph
0x00007ffc27cce000 	/System/Library/PrivateFrameworks/MLCompilerServices.framework/Versions/A/MLCompilerServices
0x00007ff83773b000 	/System/Library/PrivateFrameworks/ANEServices.framework/Versions/A/ANEServices
0x00007ff92b064000 	/usr/lib/libncurses.5.4.dylib
0x00007ff81fc5f000 	/usr/lib/libsandbox.1.dylib
0x00007ff82bf22000 	/usr/lib/libMatch.1.dylib
0x00007ffc27c6d000 	/System/Library/PrivateFrameworks/MLCompilerRuntime.framework/Versions/A/MLCompilerRuntime
0x00007ffb30847000 	/System/Library/PrivateFrameworks/CinematicFraming.framework/Versions/A/CinematicFraming
0x00007ff924ece000 	/System/Library/Frameworks/CoreMotion.framework/Versions/A/CoreMotion
0x00007ff933a12000 	/System/Library/PrivateFrameworks/TimeSync.framework/Versions/A/TimeSync
0x00007ffb35929000 	/System/Library/PrivateFrameworks/DistributedSensing.framework/Versions/A/DistributedSensing
0x00007ff92f6ca000 	/System/Library/PrivateFrameworks/MobileBluetooth.framework/Versions/A/MobileBluetooth
0x00007ff82df36000 	/System/Library/PrivateFrameworks/LocationSupport.framework/Versions/A/LocationSupport
0x00007ff9353ea000 	/System/Library/PrivateFrameworks/IOKitten.framework/Versions/A/IOKitten
0x00007ff82161f000 	/System/Library/PrivateFrameworks/CoreDuet.framework/Versions/A/CoreDuet
0x00007ff8351c9000 	/System/Library/PrivateFrameworks/CoreDuetContext.framework/Versions/A/CoreDuetContext
0x00007ffb2ebb6000 	/System/Library/PrivateFrameworks/BiomeLibrary.framework/Versions/A/BiomeLibrary
0x00007ff939429000 	/System/Library/PrivateFrameworks/BiomePubSub.framework/Versions/A/BiomePubSub
0x00007ff825bc8000 	/System/Library/Frameworks/CloudKit.framework/Versions/A/CloudKit
0x00007ff83521d000 	/System/Library/Frameworks/Intents.framework/Versions/A/Intents
0x00007ff8378d3000 	/System/Library/PrivateFrameworks/CoreDuetDaemonProtocol.framework/Versions/A/CoreDuetDaemonProtocol
0x00007ff837766000 	/System/Library/PrivateFrameworks/ProactiveSupport.framework/Versions/A/ProactiveSupport
0x00007ff933a8d000 	/System/Library/PrivateFrameworks/BiomeStreams.framework/Versions/A/BiomeStreams
0x00007ff939488000 	/System/Library/PrivateFrameworks/BiomeStorage.framework/Versions/A/BiomeStorage
0x00007ff930218000 	/System/Library/PrivateFrameworks/BiomeFoundation.framework/Versions/A/BiomeFoundation
0x00007ffc25ee9000 	/System/Library/PrivateFrameworks/IntelligencePlatformLibrary.framework/Versions/A/IntelligencePlatformLibrary
0x00007ffc2f0eb000 	/System/Library/PrivateFrameworks/PoirotSchematizer.framework/Versions/A/PoirotSchematizer
0x00007ffb2f253000 	/System/Library/PrivateFrameworks/BiomeSync.framework/Versions/A/BiomeSync
0x00007ffb2eb97000 	/System/Library/PrivateFrameworks/BiomeFlexibleStorage.framework/Versions/A/BiomeFlexibleStorage
0x00007ffb2eb7d000 	/System/Library/PrivateFrameworks/BiomeDSL.framework/Versions/A/BiomeDSL
0x00007ffc2f15c000 	/System/Library/PrivateFrameworks/PoirotUDFs.framework/Versions/A/PoirotUDFs
0x00007ffc2f0c2000 	/System/Library/PrivateFrameworks/PoirotSQLite.framework/Versions/A/PoirotSQLite
0x00007ff8361fc000 	/System/Library/PrivateFrameworks/InternationalTextSearch.framework/Versions/A/InternationalTextSearch
0x00007ffb304af000 	/System/Library/PrivateFrameworks/CascadeSets.framework/Versions/A/CascadeSets
0x00007ffb2813a000 	/System/Library/Frameworks/SharedWithYouCore.framework/Versions/A/SharedWithYouCore
0x00007ff8362c4000 	/System/Library/PrivateFrameworks/AppleAccount.framework/Versions/A/AppleAccount
0x00007ff8366c4000 	/System/Library/PrivateFrameworks/C2.framework/Versions/A/C2
0x00007ff8272a6000 	/System/Library/Frameworks/CoreLocation.framework/Versions/A/CoreLocation
0x00007ffb30b05000 	/System/Library/PrivateFrameworks/CloudAssets.framework/Versions/A/CloudAssets
0x00007ff8366bc000 	/System/Library/Frameworks/PushKit.framework/Versions/A/PushKit
0x00007ffa21388000 	/usr/lib/swift/libswiftAVFoundation.dylib
0x00007ff92620f000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x00007ff92c2c7000 	/usr/lib/swift/libswiftCoreLocation.dylib
0x00007ff92c2d9000 	/usr/lib/swift/libswiftCoreMIDI.dylib
0x00007ffa1f8d2000 	/usr/lib/swift/libswiftCoreMedia.dylib
0x00007ff82f72a000 	/System/Library/PrivateFrameworks/Sharing.framework/Versions/A/Sharing
0x00007ff835887000 	/System/Library/PrivateFrameworks/Apple80211.framework/Versions/A/Apple80211
0x00007ff829431000 	/System/Library/Frameworks/CoreWLAN.framework/Versions/A/CoreWLAN
0x00007ff83997e000 	/System/Library/PrivateFrameworks/AssistantServices.framework/Versions/A/AssistantServices
0x00007ff834281000 	/System/Library/PrivateFrameworks/SAObjects.framework/Versions/A/SAObjects
0x00007ff8395bb000 	/System/Library/PrivateFrameworks/MediaRemote.framework/Versions/A/MediaRemote
0x00007ff92b5ae000 	/System/Library/PrivateFrameworks/SiriInstrumentation.framework/Versions/A/SiriInstrumentation
0x00007ffc32275000 	/System/Library/PrivateFrameworks/SiriAnalytics.framework/Versions/A/SiriAnalytics
0x00007ffc36f94000 	/System/Library/PrivateFrameworks/SiriTTSService.framework/Versions/A/SiriTTSService
0x00007ffc32aaf000 	/System/Library/PrivateFrameworks/SiriCrossDeviceArbitration.framework/Versions/A/SiriCrossDeviceArbitration
0x00007ff9292cf000 	/System/Library/PrivateFrameworks/FeedbackLogger.framework/Versions/A/FeedbackLogger
0x00007ffc32afc000 	/System/Library/PrivateFrameworks/SiriCrossDeviceArbitrationFeedback.framework/Versions/A/SiriCrossDeviceArbitrationFeedback
0x00007ffc210fa000 	/System/Library/PrivateFrameworks/GenerativeModels.framework/Versions/A/GenerativeModels
0x00007ff91f9dc000 	/System/Library/PrivateFrameworks/MediaServices.framework/Versions/A/MediaServices
0x00007ff82f2af000 	/System/Library/PrivateFrameworks/IDS.framework/Versions/A/IDS
0x00007ff82c195000 	/System/Library/PrivateFrameworks/PersistentConnection.framework/Versions/A/PersistentConnection
0x00007ff82dec2000 	/System/Library/PrivateFrameworks/IMFoundation.framework/Versions/A/IMFoundation
0x00007ff91fb63000 	/System/Library/PrivateFrameworks/Marco.framework/Versions/A/Marco
0x00007ff82f47b000 	/System/Library/PrivateFrameworks/IDSFoundation.framework/Versions/A/IDSFoundation
0x00007ff83ae74000 	/System/Library/PrivateFrameworks/Engram.framework/Versions/A/Engram
0x00007ff83ae1a000 	/usr/lib/libtidy.A.dylib
0x00007ff93356b000 	/System/Library/PrivateFrameworks/FTAWD.framework/Versions/A/FTAWD
0x00007ff82fdf1000 	/System/Library/PrivateFrameworks/GeoServices.framework/Versions/A/GeoServices
0x00007ffc212cf000 	/System/Library/PrivateFrameworks/GeoServicesCore.framework/Versions/A/GeoServicesCore
0x00007ff920554000 	/System/Library/PrivateFrameworks/PhoneNumbers.framework/Versions/A/PhoneNumbers
0x00007ffb3544a000 	/System/Library/PrivateFrameworks/Dendrite.framework/Versions/A/Dendrite
0x00007ffb2e660000 	/System/Library/PrivateFrameworks/AtomicsInternal.framework/Versions/A/AtomicsInternal
0x00007ff92d09f000 	/usr/lib/libtailspin.dylib
0x00007ffa1f455000 	/System/Library/PrivateFrameworks/Osprey.framework/Versions/A/Osprey
0x00007ffc365c3000 	/System/Library/PrivateFrameworks/SiriTTS.framework/Versions/A/SiriTTS
0x00007ff925e58000 	/System/Library/Frameworks/NaturalLanguage.framework/Versions/A/NaturalLanguage
0x00007ffc355f9000 	/System/Library/PrivateFrameworks/SiriPowerInstrumentation.framework/Versions/A/SiriPowerInstrumentation
0x00007ff926cd2000 	/System/Library/PrivateFrameworks/Trial.framework/Versions/A/Trial
0x00007ff926c57000 	/System/Library/PrivateFrameworks/TrialProto.framework/Versions/A/TrialProto
0x00007ffa2f4ee000 	/usr/lib/swift/libswiftNaturalLanguage.dylib
0x00007ffc39929000 	/System/Library/PrivateFrameworks/TailspinSymbolication.framework/Versions/A/TailspinSymbolication
0x00007ffb351d6000 	/System/Library/PrivateFrameworks/Darwinup.framework/Versions/A/Darwinup
0x00007ff9383e9000 	/System/Library/PrivateFrameworks/SignpostSupport.framework/Versions/A/SignpostSupport
0x00007ff925d28000 	/System/Library/PrivateFrameworks/FeatureFlagsSupport.framework/Versions/A/FeatureFlagsSupport
0x00007ff937b35000 	/System/Library/PrivateFrameworks/ktrace.framework/Versions/A/ktrace
0x00007ff93868a000 	/System/Library/PrivateFrameworks/SampleAnalysis.framework/Versions/A/SampleAnalysis
0x00007ffa20bb9000 	/System/Library/PrivateFrameworks/kperfdata.framework/Versions/A/kperfdata
0x00007ff82fd11000 	/usr/lib/libdscsym.dylib
0x00007ffa210cd000 	/System/Library/PrivateFrameworks/BulkSymbolication.framework/Versions/A/BulkSymbolication
0x00007ff826052000 	/System/Library/Frameworks/CoreML.framework/Versions/A/CoreML
0x00007ffa2d9dd000 	/usr/lib/libedit.3.dylib
0x00007ff829b1b000 	/System/Library/Frameworks/MLCompute.framework/Versions/A/MLCompute
0x00007ffc27b3a000 	/System/Library/PrivateFrameworks/MLAssetIO.framework/Versions/A/MLAssetIO
0x00007ffd2970c000 	/usr/lib/swift/libswiftMLCompute.dylib
0x00007ff81e1a4000 	/System/Library/PrivateFrameworks/LanguageModeling.framework/Versions/A/LanguageModeling
0x00007ff822e61000 	/System/Library/PrivateFrameworks/Montreal.framework/Versions/A/Montreal
0x00007ff82a452000 	/usr/lib/libcmph.dylib
0x00007ffc21174000 	/System/Library/PrivateFrameworks/GenerativeModelsFoundation.framework/Versions/A/GenerativeModelsFoundation
0x00007ffd1bed9000 	/System/Library/PrivateFrameworks/TokenGeneration.framework/Versions/A/TokenGeneration
0x00007ffc20feb000 	/System/Library/PrivateFrameworks/GenerativeFunctions.framework/Versions/A/GenerativeFunctions
0x00007ffc2102f000 	/System/Library/PrivateFrameworks/GenerativeFunctionsFoundation.framework/Versions/A/GenerativeFunctionsFoundation
0x00007ffc29ad1000 	/System/Library/PrivateFrameworks/ModelCatalog.framework/Versions/A/ModelCatalog
0x00007ffc31b95000 	/System/Library/PrivateFrameworks/SensitiveContentAnalysisML.framework/Versions/A/SensitiveContentAnalysisML
0x00007ffc2108d000 	/System/Library/PrivateFrameworks/GenerativeFunctionsInstrumentation.framework/Versions/A/GenerativeFunctionsInstrumentation
0x00007ffc2fea2000 	/System/Library/PrivateFrameworks/PromptKit.framework/Versions/A/PromptKit
0x00007ffc2fb02000 	/System/Library/PrivateFrameworks/ProactiveDaemonSupport.framework/Versions/A/ProactiveDaemonSupport
0x00007ffd1c045000 	/System/Library/PrivateFrameworks/TokenGenerationCore.framework/Versions/A/TokenGenerationCore
0x00007ffd1ef76000 	/System/Library/PrivateFrameworks/VectorSearch.framework/Versions/A/VectorSearch
0x00007ffd297a8000 	/usr/lib/swift/libswiftSynchronization.dylib
0x00007ffc29c66000 	/System/Library/PrivateFrameworks/ModelManagerServices.framework/Versions/A/ModelManagerServices
0x00007ff91ff4f000 	/System/Library/PrivateFrameworks/ProactiveEventTracker.framework/Versions/A/ProactiveEventTracker
0x00007ffd1d92b000 	/System/Library/PrivateFrameworks/UnifiedAssetFramework.framework/Versions/A/UnifiedAssetFramework
0x00007ffb2dab4000 	/System/Library/PrivateFrameworks/AppleFlatBuffers.framework/Versions/A/AppleFlatBuffers
0x00007ffb2a4ba000 	/System/Library/PrivateFrameworks/AAAFoundationSwift.framework/Versions/A/AAAFoundationSwift
0x00007ff836452000 	/System/Library/PrivateFrameworks/AppleIDSSOAuthentication.framework/Versions/A/AppleIDSSOAuthentication
0x00007ff932d3b000 	/usr/lib/swift/libswiftCompression.dylib
0x00007ffc31d4a000 	/System/Library/PrivateFrameworks/SentencePieceInternal.framework/Versions/A/SentencePieceInternal
0x00007ff8321d1000 	/System/Library/Frameworks/Vision.framework/Versions/A/Vision
0x00007ffb34d0b000 	/System/Library/PrivateFrameworks/CoreSceneUnderstanding.framework/Versions/A/CoreSceneUnderstanding
0x00007ffd1fa30000 	/System/Library/PrivateFrameworks/VisionCore.framework/Versions/A/VisionCore
0x00007ff92e4a6000 	/System/Library/Frameworks/Vision.framework/libfaceCore.dylib
0x00007ff92f189000 	/System/Library/PrivateFrameworks/Futhark.framework/Versions/A/Futhark
0x00007ff932af7000 	/System/Library/PrivateFrameworks/InertiaCam.framework/Versions/A/InertiaCam
0x00007ff92ef34000 	/System/Library/PrivateFrameworks/TextRecognition.framework/Versions/A/TextRecognition
0x00007ff82bdb8000 	/System/Library/PrivateFrameworks/CVNLP.framework/Versions/A/CVNLP
0x00007ffc202aa000 	/System/Library/PrivateFrameworks/GRDBInternal.framework/Versions/A/GRDBInternal
0x00007ff8385f9000 	/System/Library/PrivateFrameworks/RTCReporting.framework/Versions/A/RTCReporting
0x00007ff8366ac000 	/System/Library/PrivateFrameworks/IntentsFoundation.framework/Versions/A/IntentsFoundation
0x00007ffb35039000 	/System/Library/PrivateFrameworks/CryptoKitPrivate.framework/Versions/A/CryptoKitPrivate
0x00007ff82f005000 	/System/Library/Frameworks/NetworkExtension.framework/Versions/A/NetworkExtension
0x00007ff92abf1000 	/System/Library/PrivateFrameworks/CaptiveNetwork.framework/Versions/A/CaptiveNetwork
0x00007ff835d88000 	/System/Library/PrivateFrameworks/EAP8021X.framework/Versions/A/EAP8021X
0x00007ffd296ff000 	/usr/lib/swift/libswiftFileProvider.dylib
0x00000001c0fdc000 	/usr/lib/libobjc-trampolines.dylib
0x00007ff938658000 	/System/Library/PrivateFrameworks/UIKitServices.framework/Versions/A/UIKitServices
0x00007ffa38bf8000 	/System/Library/Extensions/AppleIntelKBLGraphicsMTLDriver.bundle/Contents/MacOS/AppleIntelKBLGraphicsMTLDriver
0x00007ff92c2a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/Libraries/libCGInterfaces.dylib
0x00000001c8dfe000 	/Applications/WebStorm.app/Contents/jbr/Contents/Home/lib/libosxui.dylib
0x00000001dc0a9000 	/Applications/WebStorm.app/Contents/bin/libmacscreenmenu64.dylib
0x00007ffc39dfa000 	/System/Library/PrivateFrameworks/TextInputUI.framework/Versions/A/TextInputUI
0x00007ffc39e16000 	/System/Library/PrivateFrameworks/TextInputUIMacHelper.framework/Versions/A/TextInputUIMacHelper
0x00007ffc31287000 	/System/Library/PrivateFrameworks/Sage.framework/Versions/A/Sage
0x0000000215fe1000 	/Applications/WebStorm.app/Contents/bin/libnst64.dylib
0x00007ff92f277000 	/System/Library/CoreServices/RawCamera.bundle/Contents/MacOS/RawCamera


VM Arguments:
jvm_args: abort vfprintf -XX:ErrorFile=/Users/<USER>/java_error_in_webstorm_%p.log -XX:HeapDumpPath=/Users/<USER>/java_error_in_webstorm.hprof -Xms128m -Xmx2048m -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:-OmitStackTraceInFastThrow -XX:CICompilerCount=2 -XX:+IgnoreUnrecognizedVMOptions -ea -Dsun.io.useCanonCaches=false -Dsun.java2d.metal=true -Djbr.catch.SIGABRT=true -Djdk.http.auth.tunneling.disabledSchemes="" -Djdk.attach.allowAttachSelf=true -Djdk.module.illegalAccess.silent=true -Djdk.nio.maxCachedBufferSize=2097152 -Djava.util.zip.use.nio.for.zip.file.access=true -Dkotlinx.coroutines.debug=off -XX:+UnlockDiagnosticVMOptions -XX:TieredOldPercentage=100000 -Denable.non.commercial.ai.license=true -Dapple.awt.application.appearance=system -Djb.vmOptionsFile=/Applications/WebStorm.app/Contents/Resources/../bin/webstorm.vmoptions -Djava.system.class.loader=com.intellij.util.lang.PathClassLoader -Didea.vendor.name=JetBrains -Didea.paths.selector=WebStorm2024.3 -Djna.boot.library.path=/Applications/WebStorm.app/Contents/lib/jna/amd64 -Dpty4j.preferred.native.folder=/Applications/WebStorm.app/Contents/lib/pty4j -Djna.nosys=true -Djna.noclasspath=true -Dintellij.platform.runtime.repository.path=/Applications/WebStorm.app/Contents/modules/module-descriptors.jar -Didea.platform.prefix=WebStorm -Dsplash=true -Daether.connector.resumeDownloads=false -Dcompose.swing.render.on.graphics=true --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.ref=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.text=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED --add-opens=java.base/jdk.internal.vm=ALL-UNNAMED --add-opens=java.base/sun.net.dns=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED --add-opens=java.base/sun.security.ssl=ALL-UNNAMED --add-opens=java.base/sun.security.util=ALL-UNNAMED --add-opens=java.desktop/com.apple.eawt=ALL-UNNAMED --add-opens=java.desktop/com.apple.eawt.event=ALL-UNNAMED --add-opens=java.desktop/com.apple.laf=ALL-UNNAMED --add-opens=java.desktop/com.sun.java.swing=ALL-UNNAMED --add-opens=java.desktop/java.awt=ALL-UNNAMED --add-opens=java.desktop/java.awt.dnd.peer=ALL-UNNAMED --add-opens=java.desktop/java.awt.event=ALL-UNNAMED --add-opens=java.desktop/java.awt.font=ALL-UNNAMED --add-opens=java.desktop/java.awt.image=ALL-UNNAMED --add-opens=java.desktop/java.awt.peer=ALL-UNNAMED --add-opens=java.desktop/javax.swing=ALL-UNNAMED --add-opens=java.desktop/javax.swing.plaf.basic=ALL-UNNAMED --add-opens=java.desktop/javax.swing.text=ALL-UNNAMED --add-opens=java.desktop/javax.swing.text.html=ALL-UNNAMED --add-opens=java.desktop/sun.awt=ALL-UNNAMED --add-opens=java.desktop/sun.awt.datatransfer=ALL-UNNAMED --add-opens=java.desktop/sun.awt.image=ALL-UNNAMED --add-opens=java.desktop/sun.font=ALL-UNNAMED --add-opens=java.desktop/sun.java2d=ALL-UNNAMED --add-opens=java.desktop/sun.lwawt=ALL-UNNAMED --add-opens=java.desktop/sun.lwawt.macosx=ALL-UNNAMED --add-opens=java.desktop/sun.swing=ALL-UNNAMED --add-opens=java.management/sun.management=ALL-UNNAMED --add-opens=jdk.attach/sun.tools.attach=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-opens=jdk.internal.jvmstat/sun.jvmstat.monitor=ALL-UNNAMED --add-opens=jdk.jdi/com.sun.tools.jdi=ALL-UNNAMED -Dide.native.launcher=true 
java_command: com.intellij.idea.Main
java_class_path (initial): /Applications/WebStorm.app/Contents/lib/platform-loader.jar:/Applications/WebStorm.app/Contents/lib/util-8.jar:/Applications/WebStorm.app/Contents/lib/util.jar:/Applications/WebStorm.app/Contents/lib/app-client.jar:/Applications/WebStorm.app/Contents/lib/util_rt.jar:/Applications/WebStorm.app/Contents/lib/product.jar:/Applications/WebStorm.app/Contents/lib/opentelemetry.jar:/Applications/WebStorm.app/Contents/lib/app.jar:/Applications/WebStorm.app/Contents/lib/product-client.jar:/Applications/WebStorm.app/Contents/lib/lib-client.jar:/Applications/WebStorm.app/Contents/lib/stats.jar:/Applications/WebStorm.app/Contents/lib/jps-model.jar:/Applications/WebStorm.app/Contents/lib/external-system-rt.jar:/Applications/WebStorm.app/Contents/lib/rd.jar:/Applications/WebStorm.app/Contents/lib/bouncy-castle.jar:/Applications/WebStorm.app/Contents/lib/protobuf.jar:/Applications/WebStorm.app/Contents/lib/forms_rt.jar:/Applications/WebStorm.app/Contents/lib/lib.jar:/Applications/WebStorm.app/Contents/lib/externalProcess-rt.jar:/Applications/WebStorm.app/Contents/lib/groovy.jar:/Applications/WebStorm.app/Contents/lib/annotations.jar:/Applications/WebStorm.app/Contents/lib/jsch-agent.jar:/Applications/WebStorm.app/Contents/lib/kotlinx-coroutines-slf4j-1.8.0-intellij.jar:/Applications/WebStorm.app/Contents/lib/nio-fs.jar:/Applications/WebStorm.app/Contents/lib/trove.jar
Launcher Type: generic

[Global flags]
     intx CICompilerCount                          = 2                                         {product} {command line}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
    ccstr ErrorFile                                = /Users/<USER>/java_error_in_webstorm_%p.log            {product} {command line}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = /Users/<USER>/java_error_in_webstorm.hprof         {manageable} {command line}
     bool IgnoreUnrecognizedVMOptions              = true                                      {product} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 134217728                                 {product} {command line}
    uintx NonNMethodCodeHeapSize                   = 5826188                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 265522362                              {pd product} {ergonomic}
     bool OmitStackTraceInFastThrow                = false                                     {product} {command line}
    uintx ProfiledCodeHeapSize                     = 265522362                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 536870912                              {pd product} {command line}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     intx TieredOldPercentage                      = 100000                                 {diagnostic} {command line}
     bool UnlockDiagnosticVMOptions                = true                                   {diagnostic} {command line}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseNUMA                                  = false                                     {product} {ergonomic}
     bool UseNUMAInterleaving                      = false                                     {product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=/usr/bin:/bin:/usr/sbin:/sbin
SHELL=/bin/zsh
TMPDIR=/var/folders/z6/898p_mfx3mnbg9tt2m861szw0000gp/T/

Active Locale:
LC_ALL=C
LC_COLLATE=C
LC_CTYPE=C
LC_MESSAGES=C
LC_MONETARY=C
LC_NUMERIC=C
LC_TIME=C

Signal Handlers:
   SIGSEGV: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO, unblocked
    SIGBUS: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
    SIGFPE: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGPIPE: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGXFSZ: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGILL: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGUSR2: SR_handler in libjvm.dylib, mask=00000000000000000000000000000000, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGHUP: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGINT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTERM: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGQUIT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTRAP: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked


Periodic native trim disabled

JNI global refs:
JNI global refs: 159, weak refs: 197

JNI global refs memory usage: 2099, weak refs: 4625

Process memory usage:
Resident Set Size: 1618924K (19% of 8388608K total physical memory with 320072K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader com.intellij.util.lang.PathClassLoader                                          : 164M
Loader com.intellij.ide.plugins.cl.PluginClassLoader                                   : 86126K
Loader bootstrap                                                                       : 40016K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 2492K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 586K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 17248B
Loader sun.reflect.misc.MethodUtil                                                     : 4200B

Classes loaded by more than one classloader:
Class com.intellij.ultimate.PluginVerifier                                            : loaded 4 times (x 68B)
Class kotlin.collections.CollectionsKt__ReversedViewsKt                               : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$ActionNameEllipsis$1             : loaded 2 times (x 92B)
Class ai.grazie.text.TextRange$Companion                                              : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.ext.AbbreviationPatterns$English$1$1                     : loaded 2 times (x 92B)
Class ai.grazie.nlp.patterns.standard.StrictPatterns$IsApostrophe$1                   : loaded 2 times (x 75B)
Class ai.grazie.nlp.langs.LanguageISO$Companion                                       : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.standard.StrictPatterns$HasNoLetters$1                   : loaded 2 times (x 75B)
Class ai.grazie.nlp.langs.alphabet.NoneAlphabet                                       : loaded 2 times (x 73B)
Class [Lai.grazie.nlp.patterns.MatchPattern;                                          : loaded 2 times (x 66B)
Class ai.grazie.nlp.langs.Language$Companion$1                                        : loaded 2 times (x 75B)
Class kotlin.collections.AbstractList$Companion                                       : loaded 2 times (x 68B)
Class [Lai.grazie.nlp.langs.alphabet.Alphabet;                                        : loaded 2 times (x 66B)
Class ai.grazie.nlp.tokenizer.word.HeuristicWordTokenizer                             : loaded 2 times (x 83B)
Class [Lai.grazie.nlp.langs.LanguageISO;                                              : loaded 2 times (x 66B)
Class ai.grazie.nlp.tokenizer.sequence.NonDestructiveCharSequenceTokenizer            : loaded 2 times (x 67B)
Class ai.grazie.nlp.patterns.Pattern                                                  : loaded 2 times (x 67B)
Class ai.grazie.utils.mpp.RootDataLoader                                              : loaded 2 times (x 67B)
Class ai.grazie.nlp.patterns.ext.AbbreviationPatterns$German$1$2                      : loaded 2 times (x 92B)
Class kotlin.coroutines.jvm.internal.SuspendLambda                                    : loaded 2 times (x 88B)
Class ai.grazie.nlp.patterns.MatchPattern                                             : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__CollectionsKt                                 : loaded 2 times (x 68B)
Class kotlin.collections.ArraysKt___ArraysJvmKt                                       : loaded 2 times (x 68B)
Class org.apache.commons.lang3.StringUtils                                            : loaded 2 times (x 68B)
Class [Lai.grazie.nlp.langs.alphabet.Alphabet$Group;                                  : loaded 2 times (x 66B)
Class ai.grazie.nlp.langs.alphabet.Alphabet$Group                                     : loaded 2 times (x 76B)
Class kotlin.jvm.internal.FunctionBase                                                : loaded 2 times (x 67B)
Class kotlin.enums.EnumEntriesList                                                    : loaded 2 times (x 220B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsCompositeInflection$1$1        : loaded 2 times (x 92B)
Class ai.grazie.nlp.langs.Language$Companion                                          : loaded 2 times (x 68B)
Class kotlin.collections.CollectionsKt__IteratorsJVMKt                                : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.Pattern$Exclusion                                        : loaded 2 times (x 90B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$ChatTextEmoji$1                  : loaded 2 times (x 92B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsMention$1$1                    : loaded 2 times (x 92B)
Class ai.grazie.nlp.tokenizer.Tokenizer$Keys                                          : loaded 2 times (x 68B)
Class kotlin.collections.CollectionsKt__MutableCollectionsKt                          : loaded 2 times (x 68B)
Class kotlin.io.CloseableKt                                                           : loaded 2 times (x 68B)
Class ai.grazie.DataHolder$Key                                                        : loaded 2 times (x 68B)
Class kotlin.ReplaceWith                                                              : loaded 2 times (x 67B)
Class ai.grazie.text.Text                                                             : loaded 2 times (x 82B)
Class ai.grazie.DataHolder                                                            : loaded 2 times (x 69B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$Chemical$1$1                     : loaded 2 times (x 92B)
Class ai.grazie.nlp.tokenizer.Tokenizer                                               : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.DebugMetadata                                    : loaded 2 times (x 67B)
Class ai.grazie.nlp.patterns.ext.AbbreviationPatterns                                 : loaded 2 times (x 68B)
Class ai.grazie.utils.CollectionsKt                                                   : loaded 2 times (x 68B)
Class kotlin.DeprecationLevel                                                         : loaded 2 times (x 76B)
Class com.fasterxml.jackson.core.JsonProcessingException                              : loaded 2 times (x 84B)
Class kotlin.collections.MapsKt__MapsJVMKt                                            : loaded 2 times (x 68B)
Class ai.grazie.nlp.utils.Symbols                                                     : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.ext.AbbreviationPatterns$CaseDotVaryingPattern$delegate$1$2: loaded 2 times (x 76B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$ChainedName$1$1                  : loaded 2 times (x 92B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$CommandLineOption$1              : loaded 2 times (x 92B)
Class ai.grazie.nlp.patterns.ext.AbbreviationPatterns$CaseDotVaryingPattern$delegate$1$3: loaded 2 times (x 93B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsURL$1                          : loaded 2 times (x 90B)
Class kotlin.ResultKt                                                                 : loaded 2 times (x 68B)
Class ai.grazie.utils.mpp.TextRegex                                                   : loaded 2 times (x 68B)
Class kotlin.Deprecated                                                               : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt__ArraysKt                                           : loaded 2 times (x 68B)
Class ai.grazie.utils.mpp.Platform                                                    : loaded 2 times (x 76B)
Class ai.grazie.nlp.patterns.Pattern$After                                            : loaded 2 times (x 90B)
Class ai.grazie.nlp.langs.alphabet.Alphabet$Companion                                 : loaded 2 times (x 68B)
Class kotlin.collections.SetsKt__SetsJVMKt                                            : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsCommitHash$1                   : loaded 2 times (x 92B)
Class ai.grazie.nlp.patterns.ext.AbbreviationPatterns$All$1                           : loaded 2 times (x 92B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns                                  : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.standard.StrictPatterns                                  : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.AhoCorasickPattern$TrieNode                              : loaded 2 times (x 69B)
Class ai.grazie.nlp.tokenizer.NonDestructiveTokenizer                                 : loaded 2 times (x 67B)
Class ai.grazie.nlp.patterns.ext.AbbreviationPatterns$Ukrainian$1$1                   : loaded 2 times (x 92B)
Class ai.grazie.nlp.patterns.AhoCorasickPattern$AhoCorasickBuilder                    : loaded 2 times (x 69B)
Class kotlin.collections.SetsKt                                                       : loaded 2 times (x 68B)
Class ai.grazie.nlp.langs.alphabet.Alphabet$Group$1                                   : loaded 2 times (x 75B)
Class ai.grazie.nlp.patterns.Pattern$Before                                           : loaded 2 times (x 93B)
Class ai.grazie.nlp.langs.alphabet.Alphabet$Group$2                                   : loaded 2 times (x 75B)
Class kotlin.collections.MapsKt__MapsKt                                               : loaded 2 times (x 68B)
Class ai.grazie.nlp.langs.alphabet.Alphabet$Group$3                                   : loaded 2 times (x 75B)
Class ai.grazie.nlp.langs.alphabet.Alphabet$Group$4                                   : loaded 2 times (x 75B)
Class kotlin.Metadata                                                                 : loaded 2 times (x 67B)
Class kotlin.UninitializedPropertyAccessException                                     : loaded 2 times (x 79B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsIBAN$1$1                       : loaded 2 times (x 92B)
Class ai.grazie.nlp.patterns.LambdaMatchPattern                                       : loaded 2 times (x 72B)
Class [Lai.grazie.nlp.patterns.Pattern;                                               : loaded 2 times (x 66B)
Class ai.grazie.nlp.langs.alphabet.Alphabet$Group$5                                   : loaded 2 times (x 75B)
Class ai.grazie.nlp.langs.LanguageISO$Companion$1                                     : loaded 2 times (x 75B)
Class ai.grazie.nlp.langs.alphabet.Alphabet$Group$6                                   : loaded 2 times (x 75B)
Class ai.grazie.nlp.patterns.Pattern$beforeWordBoundary$1                             : loaded 2 times (x 93B)
Class ai.grazie.nlp.langs.alphabet.Alphabet$Group$7                                   : loaded 2 times (x 75B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsEmail$1                        : loaded 2 times (x 92B)
Class ai.grazie.nlp.langs.Language$Companion$all$2                                    : loaded 2 times (x 75B)
Class ai.grazie.nlp.langs.alphabet.Alphabet$Group$8                                   : loaded 2 times (x 75B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsKt                         : loaded 2 times (x 68B)
Class ai.grazie.utils.mpp.Platform$Companion                                          : loaded 2 times (x 68B)
Class kotlin.collections.CollectionsKt__CollectionsJVMKt                              : loaded 2 times (x 68B)
Class kotlin.collections.CollectionsKt__IteratorsKt                                   : loaded 2 times (x 68B)
Class ai.grazie.nlp.langs.LanguageISO                                                 : loaded 2 times (x 76B)
Class ai.grazie.nlp.patterns.ext.AbbreviationPatterns$UpperCaseSequence$1             : loaded 2 times (x 92B)
Class [Lai.grazie.nlp.patterns.AhoCorasickPattern$AhoCorasickBuilder$AutomataBuilderNode;: loaded 2 times (x 66B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsRebusAbbreviation$1            : loaded 2 times (x 92B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsFilePath$1$1                   : loaded 2 times (x 92B)
Class kotlin.enums.EnumEntries                                                        : loaded 2 times (x 67B)
Class ai.grazie.nlp.patterns.ext.AbbreviationPatterns$CaseDotVaryingPattern$delegate$lambda$9$$inlined$sortedBy$1: loaded 2 times (x 86B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsEncodedBinary$1                : loaded 2 times (x 75B)
Class com.fasterxml.jackson.core.JacksonException                                     : loaded 2 times (x 82B)
Class ai.grazie.nlp.langs.alphabet.RegexAlphabet                                      : loaded 2 times (x 73B)
Class ai.grazie.nlp.patterns.AggregatedPattern                                        : loaded 2 times (x 92B)
Class ai.grazie.utils.mpp.PlatformKt$WhenMappings                                     : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.ext.AbbreviationPatterns$latinSIUnits$1                  : loaded 2 times (x 92B)
Class kotlin.collections.AbstractList                                                 : loaded 2 times (x 194B)
Class kotlin.collections.AbstractCollection                                           : loaded 2 times (x 114B)
Class ai.grazie.nlp.patterns.Pattern$before$1                                         : loaded 2 times (x 93B)
Class ai.grazie.utils.CollectionFactory                                               : loaded 2 times (x 68B)
Class ai.grazie.utils.mpp.Resources                                                   : loaded 2 times (x 68B)
Class ai.grazie.utils.mpp.StreamDataLoader                                            : loaded 2 times (x 67B)
Class dk.brics.automaton.Transition                                                   : loaded 2 times (x 74B)
Class dk.brics.automaton.RunAutomaton                                                 : loaded 2 times (x 80B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$GeneratedId$1                    : loaded 2 times (x 92B)
Class ai.grazie.nlp.langs.alphabet.Alphabet$Group$Companion                           : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.Pattern$Companion                                        : loaded 2 times (x 68B)
Class kotlin.collections.SetsKt___SetsKt                                              : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.standard.StrictPatterns$IsNumber$1                       : loaded 2 times (x 75B)
Class ai.grazie.nlp.patterns.ext.AbbreviationPatterns$Russian$1$1                     : loaded 2 times (x 92B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsGermanNumeral$1                : loaded 2 times (x 92B)
Class kotlin.collections.CollectionsKt___CollectionsJvmKt                             : loaded 2 times (x 68B)
Class [Lai.grazie.nlp.langs.Language;                                                 : loaded 2 times (x 66B)
Class ai.grazie.nlp.langs.alphabet.Alphabet                                           : loaded 2 times (x 73B)
Class [Ldk.brics.automaton.Transition;                                                : loaded 2 times (x 66B)
Class ai.grazie.nlp.patterns.Pattern$afterWordBoundary$1                              : loaded 2 times (x 90B)
Class [Lkotlin.DeprecationLevel;                                                      : loaded 2 times (x 66B)
Class dk.brics.automaton.BasicAutomata                                                : loaded 2 times (x 68B)
Class dk.brics.automaton.TransitionComparator                                         : loaded 2 times (x 88B)
Class kotlin.collections.MapsKt___MapsJvmKt                                           : loaded 2 times (x 68B)
Class dk.brics.automaton.Automaton                                                    : loaded 2 times (x 131B)
Class ai.grazie.text.TextRange                                                        : loaded 2 times (x 68B)
Class ai.grazie.utils.mpp.DataLoader                                                  : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt___CollectionsKt                                : loaded 2 times (x 68B)
Class kotlin.annotation.Target                                                        : loaded 2 times (x 67B)
Class dk.brics.automaton.State                                                        : loaded 2 times (x 83B)
Class kotlin.annotation.Retention                                                     : loaded 2 times (x 67B)
Class ai.grazie.utils.mpp.RootStreamDataLoader                                        : loaded 2 times (x 67B)
Class kotlin.collections.MapsKt___MapsKt                                              : loaded 2 times (x 68B)
Class kotlin.collections.ArraysKt___ArraysKt                                          : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.standard.StrictPatterns$HasNumber$1                      : loaded 2 times (x 75B)
Class kotlin.collections.MapsKt__MapWithDefaultKt                                     : loaded 2 times (x 68B)
Class kotlin.collections.CollectionsKt__MutableCollectionsJVMKt                       : loaded 2 times (x 68B)
Class kotlin.enums.EnumEntriesKt                                                      : loaded 2 times (x 68B)
Class kotlin.annotation.MustBeDocumented                                              : loaded 2 times (x 67B)
Class dk.brics.automaton.BasicOperations                                              : loaded 2 times (x 68B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt                                       : loaded 2 times (x 68B)
Class kotlin.jvm.internal.Intrinsics                                                  : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$NameInitials$1                   : loaded 2 times (x 92B)
Class ai.grazie.utils.mpp.PlatformKt                                                  : loaded 2 times (x 68B)
Class ai.grazie.utils.mpp.DataLoader$Path                                             : loaded 2 times (x 69B)
Class kotlin.collections.CollectionsKt__IterablesKt                                   : loaded 2 times (x 68B)
Class kotlin.collections.MapsKt                                                       : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$TicketName$1                     : loaded 2 times (x 92B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsOrdinalNumeral$1               : loaded 2 times (x 92B)
Class kotlin.KotlinNullPointerException                                               : loaded 2 times (x 80B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$PunctuationOperator$1$1          : loaded 2 times (x 92B)
Class kotlin.collections.ArraysKt                                                     : loaded 2 times (x 68B)
Class ai.grazie.nlp.tokenizer.sequence.CharSequenceTokenizer                          : loaded 2 times (x 67B)
Class ai.grazie.utils.mpp.FromResourcesDataLoader                                     : loaded 2 times (x 92B)
Class kotlin.collections.SetsKt__SetsKt                                               : loaded 2 times (x 68B)
Class kotlin.collections.ArraysKt__ArraysJVMKt                                        : loaded 2 times (x 68B)
Class ai.grazie.nlp.patterns.RegexPattern                                             : loaded 2 times (x 92B)
Class kotlin.jvm.internal.markers.KMappedMarker                                       : loaded 2 times (x 67B)
Class ai.grazie.nlp.tokenizer.Tokenizer$Token                                         : loaded 2 times (x 69B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsGreekMetricCombination$1       : loaded 2 times (x 92B)
Class ai.grazie.nlp.patterns.AhoCorasickPattern$AhoCorasickBuilder$AutomataBuilderNode: loaded 2 times (x 69B)
Class ai.grazie.nlp.patterns.ext.AbbreviationPatterns$CaseDotVaryingPattern           : loaded 2 times (x 91B)
Class ai.grazie.nlp.langs.alphabet.SetBasedAlphabet                                   : loaded 2 times (x 73B)
Class kotlin.coroutines.jvm.internal.SuspendFunction                                  : loaded 2 times (x 67B)
Class ai.grazie.nlp.patterns.standard.LikelyPatterns$IsIPv4$1                         : loaded 2 times (x 92B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt                      : loaded 2 times (x 68B)
Class kotlin.collections.CollectionsKt                                                : loaded 2 times (x 68B)
Class [Lai.grazie.utils.mpp.Platform;                                                 : loaded 2 times (x 66B)
Class ai.grazie.nlp.langs.Language                                                    : loaded 2 times (x 76B)
Class ai.grazie.DataHolder$data$2                                                     : loaded 2 times (x 75B)
Class kotlin.NotImplementedError                                                      : loaded 2 times (x 79B)
Class ai.grazie.nlp.patterns.AhoCorasickPattern                                       : loaded 2 times (x 93B)

---------------  S Y S T E M  ---------------

OS:
uname: Darwin 24.3.0 Darwin Kernel Version 24.3.0: Thu Jan  2 20:22:00 PST 2025; root:xnu-11215.81.4~3/RELEASE_X86_64 x86_64
OS uptime: 2 days 5:37 hours
rlimit (soft/hard): STACK 8192k/65532k , CORE 0k/infinity , NPROC 1392/2088 , NOFILE 10240/infinity , AS infinity/infinity , CPU infinity/infinity , DATA infinity/infinity , FSIZE infinity/infinity , MEMLOCK infinity/infinity , RSS infinity/infinity
load average: 4.99 3.52 3.05

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
machdep.cpu.brand_string:Intel(R) Core(TM) i5-8257U CPU @ 1.40GHz
hw.cpufrequency:1400000000
hw.cpufrequency_min:1400000000
hw.cpufrequency_max:1400000000
hw.cachelinesize:64
hw.l1icachesize:32768
hw.l1dcachesize:32768
hw.l2cachesize:262144
hw.l3cachesize:6291456

Memory: 4k page, physical 8388608k(310060k free), swap 2097152k(1201664k free)

vm_info: OpenJDK 64-Bit Server VM (21.0.5+8-b631.30) for bsd-amd64 JRE (21.0.5+8-b631.30), built on 2024-12-06 by "builduser" with clang Apple LLVM 13.0.0 (clang-1300.0.29.30)

END.
