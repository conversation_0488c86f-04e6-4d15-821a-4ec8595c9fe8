# This viminfo file was generated by Vim 9.1.
# You may edit it if you're careful!

# Viminfo version
|1,4

# Value of 'encoding' when this file was written
*encoding=utf-8


# hlsearch on (H) or off (h):
~h
# Last Search Pattern:
~Msle0~/\<erge\>

# Command Line History (newest to oldest):
:wq
|2,0,1747457685,,"wq"
:q
|2,0,1747300272,,"q"
:q!
|2,0,1735808877,,"q!"
:1
|2,0,1735808865,,"1"
:!q
|2,0,1735805598,,"!q"

# Search String History (newest to oldest):
? \<erge\>
|2,1,1735040349,,"\\<erge\\>"

# Expression History (newest to oldest):

# Input Line History (newest to oldest):

# Debug Line History (newest to oldest):

# Registers:

# File marks:
'0  4  36  ~/.ansible/tmp/ansible-local-32722_2wdrmqd/tmpwslr8z2x.yml
|4,48,4,36,1747457685,"~/.ansible/tmp/ansible-local-32722_2wdrmqd/tmpwslr8z2x.yml"
'1  5  36  ~/.ansible/tmp/ansible-local-4144edijp_bn/tmp00ykrmsc.yml
|4,49,5,36,1747312954,"~/.ansible/tmp/ansible-local-4144edijp_bn/tmp00ykrmsc.yml"
'2  15  10  ~/.ssh/config
|4,50,15,10,1747300304,"~/.ssh/config"
'3  1  0  ~/backstage/.ssh/config
|4,51,1,0,1747300272,"~/backstage/.ssh/config"
'4  16  33  ~/.ssh/config
|4,52,16,33,1747294077,"~/.ssh/config"
'5  15  33  ~/.ssh/config
|4,53,15,33,1747294077,"~/.ssh/config"
'6  6  10  ~/.ssh/config
|4,54,6,10,1746372316,"~/.ssh/config"
'7  6  10  ~/.ssh/config
|4,55,6,10,1746372316,"~/.ssh/config"
'8  6  10  ~/.ssh/config
|4,56,6,10,1746372316,"~/.ssh/config"
'9  6  10  ~/.ssh/config
|4,57,6,10,1746372316,"~/.ssh/config"

# Jumplist (newest first):
-'  4  36  ~/.ansible/tmp/ansible-local-32722_2wdrmqd/tmpwslr8z2x.yml
|4,39,4,36,1747457685,"~/.ansible/tmp/ansible-local-32722_2wdrmqd/tmpwslr8z2x.yml"
-'  1  0  ~/.ansible/tmp/ansible-local-32722_2wdrmqd/tmpwslr8z2x.yml
|4,39,1,0,1747457660,"~/.ansible/tmp/ansible-local-32722_2wdrmqd/tmpwslr8z2x.yml"
-'  5  36  ~/.ansible/tmp/ansible-local-4144edijp_bn/tmp00ykrmsc.yml
|4,39,5,36,1747312954,"~/.ansible/tmp/ansible-local-4144edijp_bn/tmp00ykrmsc.yml"
-'  5  36  ~/.ansible/tmp/ansible-local-4144edijp_bn/tmp00ykrmsc.yml
|4,39,5,36,1747312954,"~/.ansible/tmp/ansible-local-4144edijp_bn/tmp00ykrmsc.yml"
-'  1  0  ~/.ansible/tmp/ansible-local-4144edijp_bn/tmp00ykrmsc.yml
|4,39,1,0,1747312916,"~/.ansible/tmp/ansible-local-4144edijp_bn/tmp00ykrmsc.yml"
-'  1  0  ~/.ansible/tmp/ansible-local-4144edijp_bn/tmp00ykrmsc.yml
|4,39,1,0,1747312916,"~/.ansible/tmp/ansible-local-4144edijp_bn/tmp00ykrmsc.yml"
-'  15  10  ~/.ssh/config
|4,39,15,10,1747300304,"~/.ssh/config"
-'  15  10  ~/.ssh/config
|4,39,15,10,1747300304,"~/.ssh/config"
-'  15  10  ~/.ssh/config
|4,39,15,10,1747300304,"~/.ssh/config"
-'  15  10  ~/.ssh/config
|4,39,15,10,1747300304,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1747300280,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1747300280,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1747300280,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1747300280,"~/.ssh/config"
-'  1  0  ~/backstage/.ssh/config
|4,39,1,0,1747300272,"~/backstage/.ssh/config"
-'  1  0  ~/backstage/.ssh/config
|4,39,1,0,1747300272,"~/backstage/.ssh/config"
-'  1  0  ~/backstage/.ssh/config
|4,39,1,0,1747300272,"~/backstage/.ssh/config"
-'  1  0  ~/backstage/.ssh/config
|4,39,1,0,1747300272,"~/backstage/.ssh/config"
-'  1  0  ~/backstage/.ssh/config
|4,39,1,0,1747300272,"~/backstage/.ssh/config"
-'  1  0  ~/backstage/.ssh/config
|4,39,1,0,1747300272,"~/backstage/.ssh/config"
-'  1  0  ~/backstage/.ssh/config
|4,39,1,0,1747300272,"~/backstage/.ssh/config"
-'  1  0  ~/backstage/.ssh/config
|4,39,1,0,1747300272,"~/backstage/.ssh/config"
-'  16  33  ~/.ssh/config
|4,39,16,33,1747294077,"~/.ssh/config"
-'  15  33  ~/.ssh/config
|4,39,15,33,1747294077,"~/.ssh/config"
-'  15  33  ~/.ssh/config
|4,39,15,33,1747294077,"~/.ssh/config"
-'  16  33  ~/.ssh/config
|4,39,16,33,1747294077,"~/.ssh/config"
-'  15  33  ~/.ssh/config
|4,39,15,33,1747294077,"~/.ssh/config"
-'  15  33  ~/.ssh/config
|4,39,15,33,1747294077,"~/.ssh/config"
-'  16  33  ~/.ssh/config
|4,39,16,33,1747294077,"~/.ssh/config"
-'  15  33  ~/.ssh/config
|4,39,15,33,1747294077,"~/.ssh/config"
-'  15  33  ~/.ssh/config
|4,39,15,33,1747294077,"~/.ssh/config"
-'  16  33  ~/.ssh/config
|4,39,16,33,1747294077,"~/.ssh/config"
-'  15  33  ~/.ssh/config
|4,39,15,33,1747294077,"~/.ssh/config"
-'  15  33  ~/.ssh/config
|4,39,15,33,1747294077,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1747294016,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1747294016,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1747294016,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1747294016,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1747294016,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1747294016,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1747294016,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1747294016,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  6  10  ~/.ssh/config
|4,39,6,10,1746372316,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1746372291,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1746372291,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1746372291,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1746372291,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1746372291,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1746372291,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1746372291,"~/.ssh/config"
-'  1  0  ~/.ssh/config
|4,39,1,0,1746372291,"~/.ssh/config"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"
-'  1  0  ~/bhansamart-Ecommerce/.git/MERGE_MSG
|4,39,1,0,1745978454,"~/bhansamart-Ecommerce/.git/MERGE_MSG"

# History of marks within files (newest to oldest):

> ~/.ansible/tmp/ansible-local-32722_2wdrmqd/tmpwslr8z2x.yml
	*	1747457684	0
	"	4	36
	^	4	37
	.	4	22
	+	4	22

> ~/.ansible/tmp/ansible-local-4144edijp_bn/tmp00ykrmsc.yml
	*	1747312953	0
	"	5	36
	^	5	37
	.	5	22
	+	5	22

> ~/.ssh/config
	*	1747300304	0
	"	15	10
	^	15	11
	.	15	10
	+	6	10
	+	16	37
	+	12	14
	+	13	26
	+	16	33
	+	15	10

> ~/backstage/.ssh/config
	*	1747300270	0
	"	1	0

> ~/bhansamart-Ecommerce/.git/MERGE_MSG
	*	1745945660	0
	"	1	0

> ~/CTEVT-MANAGEMENT/.git/MERGE_MSG
	*	1738050678	0
	"	1	0

> ~/template/.git/config
	*	1736923550	0
	"	1	5

> ~/CTEVT-MANAGEMENT/.git/COMMIT_EDITMSG
	*	1735977744	0
	"	1	0

> ~/Restaurant-Management-System/.git/MERGE_MSG
	*	1735884820	0
	"	1	0
	^	1	0
	.	1	0
	+	1	0
	+	2	0
	+	1	0
	e	1	0

> ~/.ssh/id_contabo
	*	1735813471	0
	"	27	0

> ~/Restaurant-Management-System/.git/config
	*	1735802016	0
	"	1	0
	^	9	73
	.	9	72
	+	9	72

> ~/Restaurant-Management-System/.git/COMMIT_EDITMSG
	*	1734546653	0
	"	46	0
